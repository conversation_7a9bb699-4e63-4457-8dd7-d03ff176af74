<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Work Tracker</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* Base styles */
    :root {
      --background: 0 0% 100%;
      --foreground: 222.2 84% 4.9%;
      --card: 0 0% 100%;
      --card-foreground: 222.2 84% 4.9%;
      --popover: 0 0% 100%;
      --popover-foreground: 222.2 84% 4.9%;
      --primary: 217 91% 27%;
      --primary-foreground: 210 40% 98%;
      --secondary: 210 40% 96.1%;
      --secondary-foreground: 222.2 47.4% 11.2%;
      --muted: 210 40% 96.1%;
      --muted-foreground: 215.4 16.3% 46.9%;
      --accent: 328 81% 49%;
      --accent-foreground: 210 40% 98%;
      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 210 40% 98%;
      --border: 214.3 31.8% 91.4%;
      --input: 214.3 31.8% 91.4%;
      --ring: 217 91% 27%;
      --radius: 0.5rem;
      --primary-blue: #004AAD;
      --primary-pink: #CD208B;
      --yellow: #FFD700;
      --text-dark: #000000;
      --text-light: #FFFFFF;
      --text-gray: #666;
      --white: #ffffff;
      --light-gray: #f8f9fa;
      --border-color: #e5e7eb;
      --text-medium: #6b7280;
    }



    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 5px;
    }

    ::-webkit-scrollbar-thumb {
      background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
      border-radius: 5px;
      transition: all 0.3s ease;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(to bottom, #00296b, #b0147a);
    }

    body {
      font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      background-color: hsl(var(--background));
      color: hsl(var(--foreground));
      line-height: 1.5;
    }

    .container {
      width: 100%;
      max-width: 2000px;
      margin: 0 auto;
      background-color: white;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .min-h-screen {
      min-height: 100vh;
    }

    /* Navbar Styles */
    .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 2rem;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        height: 5rem;
    }

    .navbar-left {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .logo {
        display: flex;
        align-items: center;
        text-decoration: none;
    }

    .logo img {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: 50%;
        border: 2px solid var(--primary-blue);
    }

    .logo h1 {
        font-size: 1.7rem;
        font-weight: bold;
        color: var(--primary-pink);
        margin-left: 0.5rem;
    }

    .nav-links {
        display: flex;
        gap: 2rem;
        align-items: center;
    }

    .nav-links a {
        color: var(--primary-blue);
        text-decoration: none;
        font-size: 1.1rem;
        font-weight: 500;
    }

    .nav-links a:hover {
        color: var(--primary-pink);
    }

    .nav-dropdown {
        position: relative;
        display: inline-block;
    }

    .nav-dropbtn {
        font-weight: 500;
        font-size: 1.1rem;
        color: var(--primary-blue);
        background: none;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0;
    }

    .nav-dropbtn:hover {
        color: var(--primary-pink);
    }

    .nav-dropdown-content {
        display: none;
        position: absolute;
        background-color: #fff;
        min-width: 200px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        border-radius: 8px;
        z-index: 1001;
        top: 100%;
        left: 0;
    }

    .nav-dropdown-content a {
        color: var(--primary-blue);
        padding: 12px 16px;
        text-decoration: none;
        display: block;
        font-size: 1rem;
    }

    .nav-dropdown-content a:hover {
        background-color: #f9f9f9;
        color: var(--primary-pink);
    }

    .nav-dropdown:hover .nav-dropdown-content {
        display: block;
    }

    /* Right section container */
    .right-section {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    /* Search container */
    .search-container {
        display: flex;
        align-items: center;
        position: relative;
    }

    .search-type-select {
        position: relative;
    }

    .search-type-button {
        height: 2.5rem;
        background: white;
        border: 1px solid var(--primary-blue);
        border-right: none;
        border-radius: 8px 0 0 8px;
        padding: 0 1rem;
        color: var(--primary-blue);
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .search-type-button:hover {
        background-color: #f0f7ff;
    }

    .search-bar {
        height: 2.5rem;
        display: flex;
        align-items: center;
        background: white;
        border: 1px solid var(--primary-blue);
        border-radius: 0 8px 8px 0;
        width: 220px;
        transition: all 0.3s ease;
        position: relative;
    }

    .search-bar:focus-within {
        box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
    }

    .search-bar input {
        border: none;
        outline: none;
        padding: 0 2.5rem 0 0.75rem;
        width: 100%;
        height: 100%;
        font-size: 0.95rem;
        color: #333;
    }

    .search-bar input::placeholder {
        color: #9ca3af;
    }

    .search-bar .icon {
        color: var(--primary-blue);
        width: 2.5rem;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        position: absolute;
        right: 0;
        top: 0;
        background: transparent;
    }

    .search-bar .icon:hover {
        color: var(--primary-pink);
    }

    /* Auth buttons container */
    .auth-buttons {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    /* Notification icon */
    .notification-icon {
        position: relative;
        cursor: pointer;
    }

    .notification-icon i {
        font-size: 1.5rem;
        color: var(--primary-blue);
    }

    .notification-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background-color: var(--primary-pink);
        color: white;
        border-radius: 50%;
        padding: 0.1rem 0.4rem;
        font-size: 0.8rem;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Profile button */
    .profile-button {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        cursor: pointer;
        border: 2px solid var(--primary-blue);
    }

    .profile-button img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Profile dropdown */
    .profile-dropdown {
        position: relative;
        display: inline-block;
    }

    .profile-dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 50px;
        background-color: #fff;
        min-width: 200px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        border-radius: 8px;
        z-index: 1001;
    }

    .profile-dropdown-content a {
        color: var(--primary-blue);
        padding: 12px 16px;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 1rem;
    }

    .profile-dropdown-content a i {
        width: 20px;
        text-align: center;
    }

    .profile-dropdown-content a:hover {
        background-color: #f9f9f9;
        color: var(--primary-pink);
    }

    .dropdown-divider {
        height: 1px;
        background-color: #eee;
        margin: 8px 0;
    }

    .logout-option {
        color: #dc3545 !important;
    }

    .logout-option:hover {
        background-color: #fff5f5 !important;
        color: #dc3545 !important;
    }

    /* Show dropdown on click */
    .profile-dropdown-content.show {
        display: block;
    }

    /* Mobile menu styles */
    .mobile-menu-btn {
        background: none;
        border: none;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mobile-menu-btn:hover {
        background-color: #f3f4f6;
    }

    .mobile-menu {
        display: none;
        position: absolute;
        top: 60px;
        right: 1rem;
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        width: 200px;
        z-index: 20;
        border: 1px solid #e5e7eb;
    }

    .mobile-menu.active {
        display: block;
    }

    .mobile-menu a {
        display: block;
        padding: 0.75rem 1rem;
        color: #333;
        font-size: 0.875rem;
        border-bottom: 1px solid #f3f4f6;
    }

    .mobile-menu a:last-child {
        border-bottom: none;
    }

    .mobile-menu a:hover {
        background-color: #f9fafb;
    }

    /* Responsive header styles */
    @media (max-width: 1024px) {
      .search-wrapper {
        width: 160px;
      }
    }

    @media (max-width: 768px) {
      .nav-links {
        display: none;
      }

      .mobile-menu-btn {
        display: block;
      }

      .search-wrapper {
        display: none;
      }
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
      font-weight: 600;
      line-height: 1.2;
    }

    .text-3xl {
      font-size: 1.875rem;
    }

    .text-2xl {
      font-size: 1.5rem;
    }

    .text-lg {
      font-size: 1.125rem;
    }

    .text-sm {
      font-size: 0.875rem;
    }

    .text-xs {
      font-size: 0.75rem;
    }

    .font-bold {
      font-weight: 700;
    }

    .font-semibold {
      font-weight: 600;
    }

    .font-medium {
      font-weight: 500;
    }

    .text-muted-foreground {
      color: hsl(var(--muted-foreground));
    }

    /* Layout */
    .py-8 {
      padding-top: 2rem;
      padding-bottom: 2rem;
    }

    .py-4 {
      padding-top: 1rem;
      padding-bottom: 1rem;
    }

    .px-4 {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .p-6 {
      padding: 1.5rem;
    }

    .p-4 {
      padding: 1rem;
    }

    .p-2 {
      padding: 0.5rem;
    }

    .mb-8 {
      margin-bottom: 2rem;
    }

    .mb-4 {
      margin-bottom: 1rem;
    }

    .mb-2 {
      margin-bottom: 0.5rem;
    }

    .mt-4 {
      margin-top: 1rem;
    }

    .mt-2 {
      margin-top: 0.5rem;
    }

    .mr-2 {
      margin-right: 0.5rem;
    }

    .ml-2 {
      margin-left: 0.5rem;
    }

    .flex {
      display: flex;
    }

    .flex-col {
      flex-direction: column;
    }

    .items-center {
      align-items: center;
    }

    .items-start {
      align-items: flex-start;
    }

    .justify-between {
      justify-content: space-between;
    }

    .justify-center {
      justify-content: center;
    }

    .gap-4 {
      gap: 1rem;
    }

    .gap-2 {
      gap: 0.5rem;
    }

    .grid {
      display: grid;
    }

    .grid-cols-1 {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    @media (min-width: 640px) {
      .grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
    }

    @media (min-width: 768px) {
      .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
    }

    @media (min-width: 1024px) {
      .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
      }
    }

    .space-y-4 > * + * {
      margin-top: 1rem;
    }

    .space-y-2 > * + * {
      margin-top: 0.5rem;
    }

    /* Components */
    .card {
      background-color: hsl(var(--card));
      border-radius: var(--radius);
      border: 1px solid hsl(var(--border));
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      position: relative;
    }

    .card:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    }

    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .card:hover::before {
      opacity: 1;
    }

    .card-header {
      padding: 1.25rem 1.5rem;
      border-bottom: 1px solid hsl(var(--border));
      background-color: rgba(0, 0, 0, 0.01);
    }

    .card-content {
      padding: 1.5rem;
    }

    .card-footer {
      padding: 1.25rem 1.5rem;
      border-top: 1px solid hsl(var(--border));
      background-color: rgba(0, 0, 0, 0.01);
    }

    .badge {
      display: inline-flex;
      align-items: center;
      border-radius: 9999px;
      padding: 0.25rem 0.75rem;
      font-size: 0.75rem;
      font-weight: 600;
      line-height: 1;
      white-space: nowrap;
    }

    .badge-primary {
      background-color: hsl(var(--primary));
      color: hsl(var(--primary-foreground));
    }

    .badge-secondary {
      background-color: hsl(var(--secondary));
      color: hsl(var(--secondary-foreground));
    }

    .badge-accent {
      background-color: hsl(var(--accent));
      color: hsl(var(--accent-foreground));
    }

    .badge-outline {
      background-color: transparent;
      border: 1px solid hsl(var(--border));
      color: hsl(var(--foreground));
    }

    .button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--radius);
      font-weight: 500;
      font-size: 0.875rem;
      line-height: 1;
      padding: 0.75rem 1.5rem;
      border: 1px solid transparent;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      letter-spacing: 0.01em;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .button::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: -100%;
      background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
      transition: left 0.6s ease;
    }

    .button:hover::after {
      left: 100%;
    }

    .button-default {
      background-color: var(--primary-blue);
      color: white;
    }

    .button-default:hover {
      background-color: #00296b;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 58, 140, 0.3);
    }

    .button-default:active {
      transform: translateY(1px);
      box-shadow: 0 2px 5px rgba(0, 58, 140, 0.2);
    }

    .button-outline {
      background-color: transparent;
      border: 2px solid var(--primary-blue);
      color: var(--primary-blue);
    }

    .button-outline:hover {
      background-color: rgba(0, 58, 140, 0.05);
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 58, 140, 0.1);
    }

    .button-outline:active {
      transform: translateY(1px);
      box-shadow: 0 2px 5px rgba(0, 58, 140, 0.05);
    }

    .button-destructive {
      background-color: var(--primary-pink);
      color: white;
    }

    .button-destructive:hover {
      background-color: #b0147a;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(212, 27, 140, 0.3);
    }

    .button-destructive:active {
      transform: translateY(1px);
      box-shadow: 0 2px 5px rgba(212, 27, 140, 0.2);
    }

    .button-icon {
      padding: 0.75rem;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .button-full {
      width: 100%;
    }

    .input {
      display: block;
      width: 100%;
      padding: 0.75rem 1rem;
      border-radius: var(--radius);
      border: 1.5px solid hsl(var(--input));
      background-color: transparent;
      color: hsl(var(--foreground));
      font-size: 0.875rem;
      line-height: 1.5;
      transition: all 0.3s ease;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .input:focus {
      outline: none;
      border-color: var(--primary-blue);
      box-shadow: 0 0 0 3px rgba(0, 58, 140, 0.15);
    }

    .input::placeholder {
      color: hsl(var(--muted-foreground));
      opacity: 0.7;
    }

    .input-group {
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      margin-bottom: 1.25rem;
    }

    .input-label {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-dark);
      margin-bottom: 0.25rem;
    }

    .input-hint {
      font-size: 0.75rem;
      color: hsl(var(--muted-foreground));
      margin-top: 0.25rem;
    }

    .input-error {
      font-size: 0.75rem;
      color: var(--primary-pink);
      margin-top: 0.25rem;
    }

    .input:focus {
      outline: none;
      border-color: hsl(var(--ring));
      box-shadow: 0 0 0 2px hsl(var(--ring) / 0.3);
    }

    .textarea {
      display: block;
      width: 100%;
      padding: 0.5rem;
      border-radius: var(--radius);
      border: 1px solid hsl(var(--input));
      background-color: transparent;
      color: hsl(var(--foreground));
      font-size: 0.875rem;
      line-height: 1.5;
      resize: vertical;
      min-height: 80px;
    }

    .textarea:focus {
      outline: none;
      border-color: hsl(var(--ring));
      box-shadow: 0 0 0 2px hsl(var(--ring) / 0.3);
    }

    .label {
      display: block;
      font-size: 0.875rem;
      font-weight: 500;
      margin-bottom: 0.25rem;
    }

    .tabs {
      display: flex;
      flex-direction: column;
    }

    .tabs-list {
      display: flex;
      border-bottom: 1px solid hsl(var(--border));
    }

    .tab {
      padding: 0.5rem 1rem;
      border-bottom: 2px solid transparent;
      cursor: pointer;
      font-weight: 500;
      font-size: 0.875rem;
    }

    .tab[aria-selected="true"] {
      border-bottom-color: hsl(var(--primary));
      color: hsl(var(--primary));
    }

    .tab-content {
      padding: 1rem 0;
    }

    .dialog-overlay {
      position: fixed;
      inset: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 50;
    }

    .dialog-content {
      background-color: hsl(var(--background));
      border-radius: var(--radius);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      width: 100%;
      max-width: 500px;
      padding: 1.5rem;
      position: relative;
      max-height: calc(100vh - 2rem);
      overflow-y: auto;
    }

    .dialog-header {
      margin-bottom: 1rem;
    }

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;
      margin-top: 1.5rem;
    }

    .table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      border-radius: var(--radius);
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      margin-bottom: 2rem;
    }

    .table th,
    .table td {
      padding: 1rem 1.5rem;
      text-align: left;
      border-bottom: 1px solid hsl(var(--border));
      transition: background-color 0.2s ease;
    }

    .table thead {
      background-color: rgba(0, 58, 140, 0.03);
    }

    .table th {
      font-weight: 600;
      color: var(--primary-blue);
      text-transform: uppercase;
      font-size: 0.75rem;
      letter-spacing: 0.05em;
    }

    .table tbody tr {
      transition: all 0.2s ease;
    }

    .table tbody tr:hover {
      background-color: rgba(0, 58, 140, 0.03);
      transform: translateY(-1px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .table tbody tr:last-child td {
      border-bottom: none;
    }

    .table-striped tbody tr:nth-child(odd) {
      background-color: rgba(0, 0, 0, 0.01);
    }

    .table-compact th,
    .table-compact td {
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
    }

    .table-responsive {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      margin-bottom: 1rem;
      border-radius: var(--radius);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* Custom styles */
    .header-gradient {
      background: linear-gradient(135deg, var(--primary-blue), #0052cc, #0066ff);
      color: white;
      border-radius: var(--radius);
      box-shadow: 0 10px 30px -5px rgba(0, 58, 140, 0.3);
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      transform: translateZ(0);
    }

    .header-gradient::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
      transform: rotate(30deg);
      opacity: 0;
      transition: opacity 0.5s ease;
      pointer-events: none;
      z-index: 1;
    }

    .header-gradient:hover::before {
      opacity: 1;
      animation: shine 1.5s infinite;
    }

    @keyframes shine {
      0% {
        opacity: 0.5;
        transform: rotate(30deg) translate(-30%, -30%);
      }
      100% {
        opacity: 0;
        transform: rotate(30deg) translate(30%, 30%);
      }
    }

    .header-gradient h1 {
      position: relative;
      z-index: 2;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      font-weight: 700;
      letter-spacing: 0.5px;
    }

    .header-gradient p {
      position: relative;
      z-index: 2;
      text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
      font-weight: 400;
      letter-spacing: 0.3px;
    }

    .progress-bar {
      width: 100%;
      height: 0.5rem;
      background-color: hsl(var(--muted));
      border-radius: 9999px;
      overflow: hidden;
    }

    .progress-bar-fill {
      height: 100%;
      background: linear-gradient(to right, hsl(var(--primary)), hsl(var(--primary) / 0.8));
    }

    .timer-display {
      font-family: 'Poppins', monospace;
      font-size: 3.5rem;
      font-weight: 700;
      background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 2rem 0;
      text-align: center;
      letter-spacing: 2px;
      position: relative;
      text-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .timer-display:hover {
      transform: scale(1.05);
    }

    .timer-container {
      position: relative;
      padding: 2rem;
      border-radius: var(--radius);
      background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.1);
      margin-bottom: 3rem;
      overflow: hidden;
    }

    .timer-container::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
      opacity: 0;
      transition: opacity 0.5s ease;
      pointer-events: none;
      z-index: 1;
    }

    .timer-container:hover::before {
      opacity: 1;
    }

    .file-upload {
      border: 2px dashed hsl(var(--border));
      border-radius: var(--radius);
      padding: 1.5rem;
      text-align: center;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .file-upload:hover {
      background-color: hsl(var(--muted) / 0.5);
    }

    .file-item {
      display: flex;
      align-items: center;
      padding: 0.5rem;
      background-color: hsl(var(--muted));
      border-radius: var(--radius);
    }

    .file-item-info {
      flex: 1;
      overflow: hidden;
    }

    .file-item-name {
      font-size: 0.875rem;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .file-item-size {
      font-size: 0.75rem;
      color: hsl(var(--muted-foreground));
    }

    .hidden {
      display: none;
    }

    .icon {
      width: 1rem;
      height: 1rem;
      stroke: currentColor;
      stroke-width: 2;
      stroke-linecap: round;
      stroke-linejoin: round;
      fill: none;
    }

    .icon-lg {
      width: 1.5rem;
      height: 1.5rem;
    }

    .icon-xl {
      width: 2rem;
      height: 2rem;
    }

    /* Footer Styles */
    footer {
      background: var(--primary-blue);
      padding: 2rem 5%;
      align-items: center;
      margin-top: 3rem;
    }

    .footer-grid,
    .footer-bottom {
      max-width: 85%;
      margin: 0 auto;
    }

    .footer-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 3rem;
    }

    .footer-column h3 {
      margin-bottom: 1rem;
      color: var(--text-light);
      font-weight: 600;
      font-size: 1.125rem;
    }

    .footer-column a {
      display: block;
      color: var(--text-light);
      text-decoration: none;
      margin-bottom: 0.5rem;
      transition: text-decoration 0.3s ease;
    }

    .footer-column a:hover {
      text-decoration: underline;
    }

    .footer-bottom {
      color: var(--text-light);
      text-align: center;
      padding-top: 2rem;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 2rem 0;
      flex-wrap: wrap;
      font-family: 'Poppins', sans-serif;
    }

    .footer-bottom p {
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;
      font-weight: 400;
    }

    .footer-bottom a {
      color: var(--text-light);
      margin: 0 10px;
      text-decoration: none;
    }

    .footer-bottom a:hover {
      text-decoration: underline;
    }

    .social-icons {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .social-icons a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 35px;
      height: 35px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
      margin: 0;
    }

    .social-icons .bi {
      font-size: 1.2rem;
      color: var(--text-light);
      transition: transform 0.3s ease, color 0.3s ease;
    }

    .social-icons a:hover {
      background-color: var(--primary-pink);
      transform: translateY(-3px);
    }

    /* Add responsive styles for footer */
    @media (max-width: 768px) {
      .footer-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
      }

      .footer-bottom {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .footer-bottom p {
        justify-content: center;
        font-size: 0.9rem;
      }

      .social-icons {
        justify-content: center;
      }
    }

    @media (max-width: 480px) {
      .footer-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
    }

    .pie-chart {
      width: 12rem;
      height: 12rem;
      border-radius: 50%;
      background: conic-gradient(#003a8c 0% 40%, #d41b8c 40% 70%, #8b5cf6 70% 100%);
      position: relative;
      margin: 0 auto;
    }

    .pie-chart::after {
      content: "";
      position: absolute;
      inset: 0;
      margin: auto;
      width: 7rem;
      height: 7rem;
      border-radius: 50%;
      background-color: hsl(var(--background));
    }

    .legend-item {
      display: flex;
      align-items: center;
    }

    .legend-color {
      width: 0.75rem;
      height: 0.75rem;
      border-radius: 50%;
      margin-right: 0.5rem;
    }

    /* Calendar styles */
    .calendar-container {
      width: 100%;
      overflow: hidden;
    }

    .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .calendar-nav {
      display: flex;
      gap: 0.5rem;
    }

    .calendar-title {
      font-size: 1.25rem;
      font-weight: 600;
    }

    .calendar-view-options {
      display: flex;
      gap: 0.5rem;
    }

    .calendar-view-option {
      padding: 0.25rem 0.75rem;
      border-radius: var(--radius);
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      background-color: hsl(var(--muted));
      color: hsl(var(--muted-foreground));
    }

    .calendar-view-option.active {
      background-color: hsl(var(--primary));
      color: hsl(var(--primary-foreground));
    }

    /* Weekly calendar */
    .weekly-calendar {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
    }

    .weekly-calendar th {
      padding: 0.5rem;
      text-align: center;
      font-weight: 600;
      border-bottom: 1px solid hsl(var(--border));
    }

    .weekly-calendar td {
      border: 1px solid hsl(var(--border));
      height: 3rem;
      vertical-align: top;
      padding: 0;
      position: relative;
    }

    .weekly-calendar .time-column {
      width: 4rem;
      text-align: right;
      padding-right: 0.5rem;
      font-size: 0.75rem;
      color: hsl(var(--muted-foreground));
      border-right: 1px solid hsl(var(--border));
      position: relative;
    }

    .weekly-calendar .time-column::after {
      content: "";
      position: absolute;
      top: 50%;
      right: 0;
      width: 0.25rem;
      height: 1px;
      background-color: hsl(var(--border));
    }

    .calendar-event {
      position: absolute;
      left: 0;
      width: 100%;
      background-color: hsl(var(--primary) / 0.2);
      border-left: 3px solid hsl(var(--primary));
      border-radius: 2px;
      padding: 0.25rem;
      font-size: 0.75rem;
      overflow: hidden;
      cursor: pointer;
      z-index: 10;
    }

    .calendar-event.manual {
      background-color: hsl(var(--accent) / 0.2);
      border-left-color: hsl(var(--accent));
    }

    .calendar-event-title {
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .calendar-event-time {
      font-size: 0.7rem;
      color: hsl(var(--muted-foreground));
    }

    /* Monthly calendar */
    .monthly-calendar {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
    }

    .monthly-calendar th {
      padding: 0.5rem;
      text-align: center;
      font-weight: 600;
      border-bottom: 1px solid hsl(var(--border));
    }

    .monthly-calendar td {
      border: 1px solid hsl(var(--border));
      height: 6rem;
      vertical-align: top;
      padding: 0.25rem;
    }

    .monthly-calendar .day-number {
      font-size: 0.875rem;
      font-weight: 500;
      margin-bottom: 0.25rem;
    }

    .monthly-calendar .other-month {
      color: hsl(var(--muted-foreground));
      background-color: hsl(var(--muted) / 0.2);
    }

    .monthly-calendar .today {
      background-color: hsl(var(--primary) / 0.1);
    }

    .monthly-calendar .day-events {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      overflow: hidden;
      max-height: calc(100% - 1.5rem);
    }

    .monthly-calendar .day-event {
      background-color: hsl(var(--primary) / 0.2);
      border-left: 3px solid hsl(var(--primary));
      border-radius: 2px;
      padding: 0.125rem 0.25rem;
      font-size: 0.7rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      margin-bottom: 2px;
    }

    .monthly-calendar .day-event.manual {
      background-color: hsl(var(--accent) / 0.2);
      border-left-color: hsl(var(--accent));
    }

    /* Yearly calendar */
    .yearly-calendar {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
    }

    .yearly-calendar .month {
      border: 1px solid hsl(var(--border));
      border-radius: var(--radius);
      overflow: hidden;
    }

    .yearly-calendar .month-header {
      background-color: hsl(var(--muted));
      padding: 0.5rem;
      text-align: center;
      font-weight: 600;
      border-bottom: 1px solid hsl(var(--border));
    }

    .yearly-calendar .month-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      font-size: 0.75rem;
    }

    .yearly-calendar .weekday {
      text-align: center;
      padding: 0.25rem;
      color: hsl(var(--muted-foreground));
    }

    .yearly-calendar .day {
      text-align: center;
      padding: 0.25rem;
      height: 1.5rem;
      position: relative;
      cursor: pointer;
    }

    .yearly-calendar .has-events {
      background-color: hsl(var(--primary) / 0.2);
      border-radius: 50%;
      position: relative;
    }

    .yearly-calendar .has-events::after {
      content: "";
      position: absolute;
      bottom: 2px;
      left: 50%;
      transform: translateX(-50%);
      width: 4px;
      height: 4px;
      background-color: hsl(var(--primary));
      border-radius: 50%;
    }

    /* Job card styles */
    .job-card {
      background-color: hsl(var(--card));
      border-radius: var(--radius);
      border: 1px solid hsl(var(--border));
      overflow: hidden;
      height: 100%;
      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      position: relative;
    }

    .job-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 7.5%;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .job-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    .job-card:hover::before {
      opacity: 1;
    }

    .job-card-header {
      padding: 1.5rem;
      border-bottom: 1px solid hsl(var(--border));
      background-color: rgba(0, 58, 140, 0.03);
      width: 85%;
      margin-left: auto;
      margin-right: auto;
    }

    .job-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--primary-blue);
      margin-bottom: 0.5rem;
      transition: color 0.2s ease;
    }

    .job-card:hover .job-title {
      color: var(--primary-pink);
    }

    .job-subtitle {
      font-size: 0.875rem;
      color: var(--text-medium);
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .job-subtitle::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: var(--primary-pink);
    }

    .job-card-content {
      padding: 1.5rem;
      flex: 1;
      display: grid;
      grid-template-columns: 1fr;
      gap: 1rem;
      background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, rgba(0,58,140,0.02) 100%);
      width: 85%;
      margin-left: auto;
      margin-right: auto;
    }

    .job-info-item {
      display: grid;
      grid-template-columns: auto 1fr;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem;
      border-radius: var(--radius);
      transition: background-color 0.2s ease;
    }

    .job-info-item:hover {
      background-color: rgba(0, 58, 140, 0.03);
    }

    .job-info-label {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      color: var(--text-medium);
      font-size: 0.875rem;
      font-weight: 500;
    }

    .job-info-label .icon {
      color: var(--primary-blue);
      width: 1.25rem;
      height: 1.25rem;
      transition: transform 0.2s ease;
    }

    .job-info-item:hover .job-info-label .icon {
      transform: scale(1.1);
      color: var(--primary-pink);
    }

    .job-info-value {
      font-weight: 600;
      font-size: 0.875rem;
      text-align: right;
      color: var(--text-dark);
    }

    .job-card-footer {
      padding: 1.25rem 1.5rem;
      border-top: 1px solid hsl(var(--border));
      background-color: rgba(0, 0, 0, 0.01);
      transition: background-color 0.3s ease;
      width: 85%;
      margin-left: auto;
      margin-right: auto;
    }

    .job-card:hover .job-card-footer {
      background-color: rgba(0, 58, 140, 0.03);
    }

    .job-card .button {
      transition: all 0.3s ease;
    }

    .job-card:hover .button.button-default {
      background-color: var(--primary-pink);
      box-shadow: 0 8px 20px rgba(212, 27, 140, 0.3);
    }



    /* Rating system */
    .rating {
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .rating-star {
      color: #d1d5db;
      cursor: pointer;
    }

    .rating-star.filled {
      color: #f59e0b;
    }

    /* History section */
    .history-item {
      border-bottom: 1px solid hsl(var(--border));
      padding: 1rem 0;
    }

    .history-item:last-child {
      border-bottom: none;
    }

    .history-date {
      font-size: 0.75rem;
      color: hsl(var(--muted-foreground));
    }

    .history-title {
      font-weight: 500;
      margin: 0.25rem 0;
    }

    .history-status {
      font-size: 0.875rem;
    }

    .history-rating {
      margin-top: 0.5rem;
    }

    /* Content grid layout for work history and deliverables */
    .content-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    @media (min-width: 640px) {
      .content-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (min-width: 1024px) {
      .content-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    /* Footer styles */
    footer {
        background: #003a8c;
        padding: 2rem 5%;
        align-items: center;
        margin-top: 3rem;
    }

    .footer-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 3rem;
    }

    .footer-column h3 {
        margin-bottom: 1rem;
        color: #ffffff;
    }

    .footer-column a {
        display: block;
        color: #ffffff;
        text-decoration: none;
        margin-bottom: 0.5rem;
        transition: text-decoration 0.3s ease;
    }

    .footer-column a:hover {
        text-decoration: underline;
    }

    .footer-bottom {
        color: #ffffff;
        text-align: center;
        padding-top: 2rem;
        border-top: 1px solid #ddd;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 2rem 0;
        flex-wrap: wrap;
        font-family: 'Poppins', sans-serif;
    }

    .social-icons {
        display: flex;
        gap: 1rem;
        margin-left: 1rem;
    }

    .social-icons a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        margin: 0;
    }

    .social-icons .bi {
        font-size: 1.2rem;
        color: #ffffff;
        transition: transform 0.3s ease, color 0.3s ease;
    }

    .social-icons a:hover {
        background-color: #CD208B;
        transform: translateY(-3px);
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header Section -->
    <nav class="navbar">
        <div class="navbar-left">
            <a href="{{ url_for('landing_page') }}" class="logo">
                <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                <h1>GigGenius</h1>
            </a>
            <div class="nav-links">
                <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                <a href="{{ url_for('my_proposal') }}">Proposals</a>
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Contracts <i class="bi bi-chevron-down"></i></button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('tracker') }}">Log Works</a>
                    </div>
                </div>
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Earnings <i class="bi bi-chevron-down"></i></button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                        <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                        <a href="{{ url_for('tax_info') }}">Tax Info</a>
                    </div>
                </div>
                <a href="{{ url_for('landing_page') }}">Messages</a>
            </div>
        </div>
        <div class="right-section">
            <div class="search-container">
                <div class="search-type-select">
                    <button id="searchTypeBtn" class="search-type-button">
                        <span id="selectedSearchType">Gigs</span>
                        <i class="bi bi-chevron-down"></i>
                    </button>
                </div>
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Search for gigs...">
                    <div class="icon">
                        <i class="bi bi-search"></i>
                    </div>
                </div>
            </div>
            <div class="auth-buttons">
                <div class="notification-icon">
                    <i class="bi bi-bell"></i>
                    <div class="notification-badge">3</div>
                </div>
                <div class="profile-dropdown">
                    <div class="profile-button">
                        <img src="{{ url_for('get_profile_photo', user_id=session.get('user_id', 0)) }}" alt="Profile Picture"
                             onerror="this.src=`https://ui-avatars.com/api/?name=${encodeURIComponent('{{ session.get('first_name', ' ')[0] }}{{ session.get('last_name', ' ')[0] }}')}&background=2563eb&color=fff&size=128`">
                    </div>
                    <div class="profile-dropdown-content">
                        <a href="{{ url_for('genius_profile') }}"><i class="bi bi-person"></i> Your Profile</a>
                        <a href="#"><i class="bi bi-gear"></i> Settings</a>
                        <a href="#"><i class="bi bi-question-circle"></i> Help Center</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="logout-option"><i class="bi bi-box-arrow-right"></i> Logout</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="mobile-menu" id="mobileMenu">
        <a href="{{ url_for('genius_page') }}">Find Gigs</a>
        <a href="{{ url_for('my_proposal') }}">Proposals</a>
        <a href="#">Contracts</a>
        <a href="{{ url_for('tracker') }}">Log Works</a>
        <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
        <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
        <a href="{{ url_for('tax_info') }}">Tax Info</a>
        <a href="{{ url_for('landing_page') }}">Messages</a>
        <a href="{{ url_for('genius_profile') }}">Profile</a>
    </div>



    <main class="min-h-screen">
    <!-- Job List View -->
    <div id="job-list-view" class="container py-8">
      <div class="header-gradient-container">
        <div class="header-gradient p-6 mb-8">
          <h1 class="text-3xl font-bold mb-2">Active Work</h1>
          <p class="opacity-90">Track your time and earnings across all your projects</p>
        </div>
      </div>

      <style>
        .header-gradient-container {
          width: 100%;
          display: flex;
          justify-content: center;
          margin-bottom: 2rem;
        }

        .header-gradient {
          width: 85%;
          border-radius: var(--radius);
        }
      </style>

      <h2 class="section-title">Active Work</h2>
      <div id="jobs-grid" class="active-work-grid">
        <!-- Jobs will be inserted here by JavaScript -->
      </div>

      <style>
        .section-title {
          font-size: 1.75rem;
          font-weight: 700;
          color: var(--primary-blue);
          margin-bottom: 1.5rem;
          position: relative;
          padding-bottom: 0.75rem;
          max-width: 85%;
          margin-left: auto;
          margin-right: auto;
        }



        .section-title::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 60px;
          height: 4px;
          background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
          border-radius: 2px;
        }

        .active-work-grid {
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 2rem;
          margin-bottom: 3rem;
          max-width: 85%;
          margin-left: auto;
          margin-right: auto;
        }

        @media (min-width: 768px) {
          .active-work-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }

        @media (min-width: 1200px) {
          .active-work-grid {
            grid-template-columns: repeat(3, 1fr);
          }
        }
      </style>
    </div>

    <!-- Dashboard View (initially hidden) -->
    <div id="dashboard-view" class="container py-8 hidden">
      <div class="header-gradient-container">
        <div class="header-gradient p-6 mb-8 flex items-start gap-4">
          <button id="back-button" class="button button-outline" style="background-color: rgba(255, 255, 255, 0.2); color: white;">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M19 12H5M12 19l-7-7 7-7"></path>
            </svg>
          </button>
          <div>
            <h1 id="job-title" class="text-2xl font-bold"></h1>
            <p id="job-subtitle" class="opacity-90"></p>
          </div>
        </div>
      </div>

      <!-- Dashboard Stats Grid -->
      <div class="dashboard-stats-grid mb-8">
        <!-- Total Hours Card -->
        <div class="dashboard-card">
          <div class="dashboard-card-header">
            <h3 class="dashboard-card-title">Total Hours</h3>
            <div class="dashboard-card-icon">
              <svg class="icon" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
            </div>
          </div>
          <div class="dashboard-card-content">
            <div class="dashboard-card-value" id="status-value">12.5 hrs</div>
            <div class="dashboard-card-subtitle" id="status-subtitle">Contract hours: 40 hrs</div>
          </div>
        </div>

        <!-- Total Earnings Card -->
        <div class="dashboard-card">
          <div class="dashboard-card-header">
            <h3 class="dashboard-card-title">Total Earnings</h3>
            <div class="dashboard-card-icon">
              <svg class="icon" viewBox="0 0 24 24">
                <line x1="12" y1="1" x2="12" y2="23"></line>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </div>
          </div>
          <div class="dashboard-card-content">
            <div class="dashboard-card-value" id="earnings-value">$312.50</div>
            <div class="dashboard-card-progress">
              <div class="dashboard-progress-bar">
                <div class="dashboard-progress-fill" style="width: 31%"></div>
              </div>
            </div>
            <div class="dashboard-card-subtitle" id="earnings-subtitle">$312.50/$1,000.00 (31%)</div>
          </div>
        </div>

        <!-- Contract Rate Card -->
        <div class="dashboard-card">
          <div class="dashboard-card-header">
            <h3 class="dashboard-card-title" id="rate-label">Contract Rate</h3>
            <div class="dashboard-card-icon">
              <svg class="icon" viewBox="0 0 24 24">
                <line x1="12" y1="1" x2="12" y2="23"></line>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </div>
          </div>
          <div class="dashboard-card-content">
            <div class="dashboard-card-value" id="contract-value">$1,000.00</div>
            <div class="dashboard-card-badge">
              <span id="job-status-badge" class="badge badge-accent">Active</span>
            </div>
          </div>
        </div>
      </div>

      <style>
        /* Dashboard Stats Grid Styles */
        .dashboard-stats-grid {
          display: grid;
          grid-template-columns: 1fr;
          gap: 1.5rem;
          margin-bottom: 2rem;
          max-width: 85%;
          margin-left: auto;
          margin-right: auto;
        }

        @media (min-width: 768px) {
          .dashboard-stats-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }

        @media (min-width: 1024px) {
          .dashboard-stats-grid {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        /* Dashboard Card Styles */
        .dashboard-card {
          background-color: white;
          border-radius: 0.75rem;
          overflow: hidden;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          transition: transform 0.3s ease, box-shadow 0.3s ease;
          border: 1px solid #e5e7eb;
          position: relative;
        }

        .dashboard-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .dashboard-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 3px;
          background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .dashboard-card:hover::before {
          opacity: 1;
        }

        .dashboard-card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1.25rem 1.5rem;
          border-bottom: 1px solid #e5e7eb;
          background-color: rgba(0, 0, 0, 0.01);
        }

        .dashboard-card-title {
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--primary-blue);
          margin: 0;
        }

        .dashboard-card-icon {
          color: var(--primary-pink);
        }

        .dashboard-card-content {
          padding: 1.5rem;
        }

        .dashboard-card-value {
          font-size: 2rem;
          font-weight: 700;
          color: var(--text-dark);
          margin-bottom: 0.75rem;
        }

        .dashboard-card-subtitle {
          font-size: 0.75rem;
          color: var(--text-medium);
        }

        .dashboard-card-badge {
          margin-top: 0.75rem;
        }

        .dashboard-card-badge .badge {
          background-color: var(--primary-pink);
          color: white;
          font-size: 0.75rem;
          font-weight: 600;
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
        }

        /* Progress Bar Styles */
        .dashboard-card-progress {
          margin: 0.75rem 0;
        }

        .dashboard-progress-bar {
          height: 6px;
          background-color: rgba(0, 0, 0, 0.05);
          border-radius: 3px;
          overflow: hidden;
        }

        .dashboard-progress-fill {
          height: 100%;
          background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
          border-radius: 3px;
          transition: width 0.5s ease;
        }
      </style>

      <!-- Time Tracking Dashboard -->
      <div id="time-tracking-dashboard" class="hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8" style="max-width: 85%; margin-left: auto; margin-right: auto;">
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-semibold">Time Tracker</h3>
              <p class="text-sm text-muted-foreground">Track your time for this project</p>
            </div>
            <div class="card-content flex flex-col items-center">
              <div id="timer-display" class="timer-display">00:00:00</div>
              <div id="hours-used" class="text-sm text-muted-foreground mb-4"></div>
              <div class="w-full progress-bar mb-6">
                <div id="hours-progress" class="progress-bar-fill" style="width: 0%"></div>
              </div>
            </div>
            <div class="card-footer flex justify-center gap-4">
              <button id="timer-button" class="button button-default">
                <svg class="icon mr-2" viewBox="0 0 24 24">
                  <polygon points="5 3 19 12 5 21 5 3"></polygon>
                </svg>
                Start
              </button>
              <button id="manual-entry-button" class="button button-outline">
                <svg class="icon mr-2" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                Manual Entry
              </button>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-semibold">Time Distribution</h3>
              <p class="text-sm text-muted-foreground">Hours worked by time of day</p>
            </div>
            <div class="card-content">
              <div class="pie-chart"></div>
            </div>
            <div class="card-footer grid grid-cols-2 gap-2">
              <div class="legend-item">
                <div class="legend-color" style="background-color: #003a8c;"></div>
                <span class="text-sm">Morning (40%)</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #d41b8c;"></div>
                <span class="text-sm">Afternoon (30%)</span>
              </div>
              <div class="legend-item">
                <div class="legend-color" style="background-color: #8b5cf6;"></div>
                <span class="text-sm">Evening (30%)</span>
              </div>
            </div>
          </div>
        </div>

        <div class="card" style="max-width: 85%; margin-left: auto; margin-right: auto;">
          <div class="card-header">
            <div class="tabs">
              <div class="tabs-list">
                <button class="tab" data-tab="logs" aria-selected="true">Time Logs</button>
                <button class="tab" data-tab="calendar">Calendar</button>
                <button class="tab" data-tab="history">Work History</button>
              </div>
            </div>
          </div>
          <div class="card-content">
            <div class="tab-content" data-tab-content="logs">
              <div class="rounded-md border">
                <table class="table" id="time-logs-table">
                  <thead>
                    <tr>
                      <th>Date</th>
                      <th>Time In</th>
                      <th>Time Out</th>
                      <th>Duration</th>
                      <th>Type</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Time logs will be inserted here by JavaScript -->
                  </tbody>
                </table>
              </div>
            </div>
            <div class="tab-content hidden" data-tab-content="calendar">
              <div id="calendar-container" class="calendar-container">
                <div class="calendar-header">
                  <div class="calendar-nav">
                    <button id="prev-period" class="button button-outline">
                      <svg class="icon" viewBox="0 0 24 24">
                        <polyline points="15 18 9 12 15 6"></polyline>
                      </svg>
                    </button>
                    <button id="next-period" class="button button-outline">
                      <svg class="icon" viewBox="0 0 24 24">
                        <polyline points="9 18 15 12 9 6"></polyline>
                      </svg>
                    </button>
                  </div>
                  <h3 id="calendar-title" class="calendar-title">April 6 - April 12, 2025</h3>
                  <div class="calendar-view-options">
                    <button class="calendar-view-option" data-view="yearly">Yearly</button>
                    <button class="calendar-view-option" data-view="monthly">Monthly</button>
                    <button class="calendar-view-option active" data-view="weekly">Weekly</button>
                  </div>
                </div>

                <!-- Weekly Calendar View (default) -->
                <div id="weekly-view" class="calendar-view">
                  <table class="weekly-calendar">
                    <thead>
                      <tr>
                        <th></th>
                        <th>Sun 6</th>
                        <th>Mon 7</th>
                        <th>Tue 8</th>
                        <th>Wed 9</th>
                        <th>Thu 10</th>
                        <th>Fri 11</th>
                        <th>Sat 12</th>
                      </tr>
                    </thead>
                    <tbody id="weekly-calendar-body">
                      <!-- Time slots will be generated by JavaScript -->
                    </tbody>
                  </table>
                </div>

                <!-- Monthly Calendar View (initially hidden) -->
                <div id="monthly-view" class="calendar-view hidden">
                  <table class="monthly-calendar">
                    <thead>
                      <tr>
                        <th>Sun</th>
                        <th>Mon</th>
                        <th>Tue</th>
                        <th>Wed</th>
                        <th>Thu</th>
                        <th>Fri</th>
                        <th>Sat</th>
                      </tr>
                    </thead>
                    <tbody id="monthly-calendar-body">
                      <!-- Days will be generated by JavaScript -->
                    </tbody>
                  </table>
                </div>

                <!-- Yearly Calendar View (initially hidden) -->
                <div id="yearly-view" class="calendar-view hidden">
                  <div id="yearly-calendar-grid" class="yearly-calendar">
                    <!-- Months will be generated by JavaScript -->
                  </div>
                </div>
              </div>
            </div>
            <div class="tab-content hidden" data-tab-content="history">
              <div class="space-y-4" id="work-history-container">
                <!-- Work history will be inserted here by JavaScript -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- One-time Dashboard -->
      <div id="one-time-dashboard" class="hidden">
        <div class="card mb-8" style="max-width: 85%; margin-left: auto; margin-right: auto;">
          <div class="card-header">
            <h3 class="text-lg font-semibold">Project Deliverables</h3>
            <p class="text-sm text-muted-foreground">Upload your completed work for this project</p>
          </div>
          <div class="card-content" id="deliverables-container">
            <!-- Deliverables will be inserted here by JavaScript -->
          </div>
          <div class="card-footer flex justify-between">
            <button id="view-contract-button" class="button button-outline">
              <svg class="icon mr-2" viewBox="0 0 24 24">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              View Contract
            </button>
            <button id="upload-project-button" class="button button-default">
              <svg class="icon mr-2" viewBox="0 0 24 24">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="17 8 12 3 7 8"></polyline>
                <line x1="12" y1="3" x2="12" y2="15"></line>
              </svg>
              Upload Project
            </button>
          </div>
        </div>
      </div>

      <!-- Milestone Dashboard -->
      <div id="milestone-dashboard" class="hidden">
        <div class="card mb-8" style="max-width: 85%; margin-left: auto; margin-right: auto;">
          <div class="card-header">
            <h3 class="text-lg font-semibold">Project Milestones</h3>
            <p class="text-sm text-muted-foreground">Complete and submit each milestone to receive payment</p>
          </div>
          <div class="card-content">
            <div id="milestones-container" class="space-y-6">
              <!-- Milestones will be inserted here by JavaScript -->
            </div>
          </div>
          <div class="card-footer">
            <button id="view-milestone-contract-button" class="button button-outline">
              <svg class="icon mr-2" viewBox="0 0 24 24">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              View Contract
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Dialogs -->
    <!-- Manual Time Entry Dialog -->
    <div id="manual-entry-dialog" class="dialog-overlay hidden">
      <div class="dialog-content">
        <div class="dialog-header">
          <h2 class="text-lg font-semibold">Manual Time Entry</h2>
        </div>
        <div class="grid gap-4 py-4">
          <p class="text-sm text-muted-foreground mb-2">
            Use this form to manually enter time when you couldn't track automatically or need additional hours beyond
            your contract limit.
          </p>
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <label for="date" class="label">Date</label>
              <input id="date" name="date" type="date" class="input" />
            </div>
            <div class="space-y-2">
              <label for="timeIn" class="label">Time In</label>
              <input id="timeIn" name="timeIn" type="time" class="input" />
            </div>
            <div class="space-y-2">
              <label for="timeOut" class="label">Time Out</label>
              <input id="timeOut" name="timeOut" type="time" class="input" />
            </div>
          </div>
          <div class="space-y-2">
            <label for="notes" class="label">Explanation (required)</label>
            <textarea id="notes" name="notes" placeholder="Explain why manual entry is needed" class="textarea"></textarea>
          </div>
          <div class="space-y-2">
            <label class="label">Proof (required)</label>
            <div class="file-upload">
              <input type="file" id="proof" class="hidden" accept="image/*,video/*" />
              <label for="proof" class="cursor-pointer flex flex-col items-center">
                <svg class="icon-lg mb-2 text-muted-foreground" viewBox="0 0 24 24">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
                <span class="text-sm text-muted-foreground">Click to upload image or video proof</span>
              </label>
            </div>
            <div id="proof-preview" class="mt-2 hidden">
              <div class="w-full max-h-40 overflow-hidden rounded-md border">
                <img id="proof-image" src="/placeholder.svg" alt="Proof preview" class="w-full h-auto object-contain" />
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-footer">
          <button id="cancel-manual-entry" class="button button-outline">Cancel</button>
          <button id="submit-manual-entry" class="button button-default">Submit</button>
        </div>
      </div>
    </div>

    <!-- Upload Project Dialog -->
    <div id="upload-project-dialog" class="dialog-overlay hidden">
      <div class="dialog-content">
        <div class="dialog-header">
          <h2 class="text-lg font-semibold">Upload Project Deliverables</h2>
        </div>
        <div class="grid gap-4 py-4">
          <div class="space-y-2">
            <label for="project-description" class="label">Project Description</label>
            <textarea id="project-description" placeholder="Describe what you're delivering" class="textarea"></textarea>
          </div>
          <div class="space-y-2">
            <label class="label">Project Files</label>
            <div class="file-upload">
              <input type="file" id="project-files" class="hidden" multiple />
              <label for="project-files" class="cursor-pointer flex flex-col items-center">
                <svg class="icon-xl mb-2 text-muted-foreground" viewBox="0 0 24 24">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
                <span class="text-sm font-medium mb-1">Click to upload files</span>
                <span class="text-xs text-muted-foreground">Upload any file format up to 50MB</span>
              </label>
            </div>
            <div id="project-files-preview" class="mt-4 space-y-2 hidden">
              <!-- File previews will be inserted here by JavaScript -->
            </div>
          </div>
        </div>
        <div class="dialog-footer">
          <button id="cancel-upload" class="button button-outline">Cancel</button>
          <button id="submit-upload" class="button button-default">Upload</button>
        </div>
      </div>
    </div>

    <!-- Contract Dialog -->
    <div id="contract-dialog" class="dialog-overlay hidden">
      <div class="dialog-content">
        <div class="dialog-header">
          <h2 class="text-lg font-semibold">Project Contract</h2>
        </div>
        <div class="border rounded-md p-4 max-h-[60vh] overflow-y-auto">
          <h4 id="contract-title" class="font-semibold mb-2"></h4>
          <p id="contract-subtitle" class="text-sm text-muted-foreground mb-4"></p>

          <div class="space-y-4">
            <div>
              <h5 class="font-medium">Project Scope</h5>
              <p id="contract-scope" class="text-sm mt-1"></p>
            </div>

            <div>
              <h5 class="font-medium">Payment Terms</h5>
              <p id="contract-payment" class="text-sm mt-1"></p>
            </div>

            <div id="contract-milestones-section" class="hidden">
              <h5 class="font-medium">Milestones</h5>
              <div id="contract-milestones" class="mt-2 space-y-2">
                <!-- Milestones will be inserted here by JavaScript -->
              </div>
            </div>

            <div>
              <h5 class="font-medium">Intellectual Property</h5>
              <p id="contract-ip" class="text-sm mt-1"></p>
            </div>
          </div>
        </div>
        <div class="dialog-footer">
          <button id="close-contract" class="button button-default">Close</button>
        </div>
      </div>
    </div>

    <!-- Time Log Details Dialog -->
    <div id="time-log-dialog" class="dialog-overlay hidden">
      <div class="dialog-content">
        <div class="dialog-header">
          <h2 class="text-lg font-semibold">Time Log Details</h2>
        </div>
        <div id="time-log-details" class="space-y-4">
          <!-- Time log details will be inserted here by JavaScript -->
        </div>
        <div class="dialog-footer">
          <button id="close-time-log" class="button button-default">Close</button>
        </div>
      </div>
    </div>

    <!-- Submit Milestone Dialog -->
    <div id="submit-milestone-dialog" class="dialog-overlay hidden">
      <div class="dialog-content">
        <div class="dialog-header">
          <h2 id="milestone-title" class="text-lg font-semibold">Submit Milestone</h2>
        </div>
        <div class="grid gap-4 py-4">
          <div class="space-y-2">
            <label for="milestone-description" class="label">Submission Description</label>
            <textarea id="milestone-description" placeholder="Describe what you're delivering for this milestone" class="textarea"></textarea>
          </div>
          <div class="space-y-2">
            <label class="label">Milestone Files</label>
            <div class="file-upload">
              <input type="file" id="milestone-files" class="hidden" multiple />
              <label for="milestone-files" class="cursor-pointer flex flex-col items-center">
                <svg class="icon-xl mb-2 text-muted-foreground" viewBox="0 0 24 24">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
                <span class="text-sm font-medium mb-1">Click to upload files</span>
                <span class="text-xs text-muted-foreground">Upload any file format up to 50MB</span>
              </label>
            </div>
            <div id="milestone-files-preview" class="mt-4 space-y-2 hidden">
              <!-- File previews will be inserted here by JavaScript -->
            </div>
          </div>
        </div>
        <div class="dialog-footer">
          <button id="cancel-milestone" class="button button-outline">Cancel</button>
          <button id="submit-milestone" class="button button-default">Submit</button>
        </div>
      </div>
    </div>

    <!-- Calendar Event Dialog -->
    <div id="calendar-event-dialog" class="dialog-overlay hidden">
      <div class="dialog-content">
        <div class="dialog-header">
          <h2 class="text-lg font-semibold">Event Details</h2>
        </div>
        <div id="calendar-event-details" class="space-y-4">
          <!-- Event details will be inserted here by JavaScript -->
        </div>
        <div class="dialog-footer">
          <button id="close-calendar-event" class="button button-default">Close</button>
        </div>
      </div>
    </div>

    <!-- Rate Work Dialog -->
    <div id="rate-work-dialog" class="dialog-overlay hidden">
      <div class="dialog-content">
        <div class="dialog-header">
          <h2 class="text-lg font-semibold">Rate Your Experience</h2>
        </div>
        <div class="grid gap-4 py-4">
          <p class="text-sm text-muted-foreground">How would you rate your experience working on this project?</p>
          <div class="flex justify-center mb-4">
            <div class="rating" id="rating-stars">
              <svg class="icon-lg rating-star" data-rating="1" viewBox="0 0 24 24">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <svg class="icon-lg rating-star" data-rating="2" viewBox="0 0 24 24">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <svg class="icon-lg rating-star" data-rating="3" viewBox="0 0 24 24">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <svg class="icon-lg rating-star" data-rating="4" viewBox="0 0 24 24">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <svg class="icon-lg rating-star" data-rating="5" viewBox="0 0 24 24">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
            </div>
          </div>
          <div class="space-y-2">
            <label for="rating-comments" class="label">Comments (optional)</label>
            <textarea id="rating-comments" placeholder="Share your thoughts about this project" class="textarea"></textarea>
          </div>
        </div>
        <div class="dialog-footer">
          <button id="cancel-rating" class="button button-outline">Cancel</button>
          <button id="submit-rating" class="button button-default">Submit Rating</button>
        </div>
      </div>
    </div>
  </main>

  <script>
    // Sample data
    const jobsData = [
      // Time Tracking Jobs
      {
        id: 1,
        type: "time-tracking",
        title: "Web Development Specialist",
        client: "TechSolutions Inc",
        clientContact: "James Wilson",
        startDate: "Feb 15, 2024",
        status: "Active",
        rate: 25,
        contractHours: 40,
        totalHours: 12.5,
        totalEarnings: 312.5,
        totalContract: 1000,
        timeLogs: [
          {
            id: 101,
            date: "2024-05-10",
            timeIn: "09:30:00",
            timeOut: "12:00:00",
            duration: "02:30:00",
            isManual: false,
          },
          {
            id: 102,
            date: "2024-05-08",
            timeIn: "13:15:00",
            timeOut: "16:30:00",
            duration: "03:15:00",
            isManual: false,
          },
          {
            id: 103,
            date: "2024-05-05",
            timeIn: "10:00:00",
            timeOut: "11:45:00",
            duration: "01:45:00",
            isManual: false,
          },
        ],
        workHistory: [
          {
            id: 1001,
            date: "2024-05-01",
            title: "Initial Project Assignment",
            description: "Assigned to develop responsive web interface for client dashboard",
            status: "Completed",
            rating: 5
          },
          {
            id: 1002,
            date: "2024-05-08",
            title: "Feature Implementation",
            description: "Added data visualization components to dashboard",
            status: "Completed",
            rating: 4
          }
        ]
      },
      {
        id: 2,
        type: "time-tracking",
        title: "UI/UX Design Consultant",
        client: "Creative Minds",
        clientContact: "Sarah Chen",
        startDate: "Mar 5, 2024",
        status: "Active",
        rate: 35,
        contractHours: 30,
        totalHours: 8.75,
        totalEarnings: 306.25,
        totalContract: 1050,
        timeLogs: [
          {
            id: 201,
            date: "2024-05-12",
            timeIn: "10:15:00",
            timeOut: "12:00:00",
            duration: "01:45:00",
            isManual: false,
          },
          {
            id: 202,
            date: "2024-05-09",
            timeIn: "14:00:00",
            timeOut: "16:30:00",
            duration: "02:30:00",
            isManual: false,
          },
          {
            id: 203,
            date: "2024-05-07",
            timeIn: "09:30:00",
            timeOut: "11:00:00",
            duration: "01:30:00",
            isManual: true,
            notes: "Internet was down during this session, so I'm adding the time manually.",
            proofName: "screenshot.png",
          },
        ],
        workHistory: [
          {
            id: 2001,
            date: "2024-03-10",
            title: "Design System Creation",
            description: "Created comprehensive design system for client's brand",
            status: "Completed",
            rating: 5
          },
          {
            id: 2002,
            date: "2024-04-15",
            title: "User Testing Session",
            description: "Conducted user testing for new interface designs",
            status: "Completed",
            rating: 4
          }
        ]
      },

      // One-time Pay Jobs
      {
        id: 3,
        type: "one-time",
        title: "Logo Design Project",
        client: "Startup Ventures",
        clientContact: "Michael Brown",
        startDate: "Apr 10, 2024",
        status: "In Progress",
        totalEarnings: 0,
        totalContract: 500,
        deliverables: [],
        workHistory: [
          {
            id: 3001,
            date: "2024-04-10",
            title: "Project Assignment",
            description: "Assigned to create logo design for new startup",
            status: "In Progress",
            rating: null
          }
        ]
      },
      {
        id: 4,
        type: "one-time",
        title: "Marketing Campaign Assets",
        client: "Global Media",
        clientContact: "Jessica Lee",
        startDate: "Apr 15, 2024",
        status: "In Progress",
        totalEarnings: 0,
        totalContract: 750,
        deliverables: [
          {
            id: 401,
            date: "2024-04-20T14:30:00Z",
            description: "Initial concept designs for social media campaign",
            files: [
              {
                name: "social-media-concepts.pdf",
                size: 2457600,
                type: "application/pdf",
              },
              {
                name: "banner-mockups.zip",
                size: 15728640,
                type: "application/zip",
              },
            ],
          },
        ],
        workHistory: [
          {
            id: 4001,
            date: "2024-04-15",
            title: "Project Assignment",
            description: "Assigned to create marketing assets for Q3 campaign",
            status: "In Progress",
            rating: null
          },
          {
            id: 4002,
            date: "2024-04-20",
            title: "Initial Concepts Submitted",
            description: "Submitted first round of concept designs",
            status: "Under Review",
            rating: null
          }
        ]
      },

      // Milestone Jobs
      {
        id: 5,
        type: "milestone",
        title: "E-commerce Website Development",
        client: "Fashion Boutique",
        clientContact: "Emma Davis",
        startDate: "Mar 20, 2024",
        status: "Active",
        totalEarnings: 600,
        totalContract: 3000,
        milestones: [
          {
            id: 501,
            title: "Website Design & Wireframes",
            description: "Create wireframes and design mockups for all pages of the e-commerce website.",
            amount: 600,
            percentOfTotal: 20,
            dueDate: "2024-04-05",
            status: "approved",
            submission: {
              date: "2024-04-03T16:45:00Z",
              description: "Completed wireframes and design mockups for all pages as requested.",
              files: [
                {
                  name: "wireframes.pdf",
                  size: 3145728,
                  type: "application/pdf",
                },
                {
                  name: "design-mockups.zip",
                  size: 20971520,
                  type: "application/zip",
                },
              ],
            },
          },
          {
            id: 502,
            title: "Frontend Development",
            description: "Develop the frontend of the website based on approved designs.",
            amount: 900,
            percentOfTotal: 30,
            dueDate: "2024-05-10",
            status: "in_progress",
          },
          {
            id: 503,
            title: "Backend Integration",
            description: "Integrate payment gateway, user authentication, and product management.",
            amount: 900,
            percentOfTotal: 30,
            dueDate: "2024-06-15",
            status: "not_started",
          },
          {
            id: 504,
            title: "Testing & Launch",
            description: "Perform testing, bug fixes, and launch the website.",
            amount: 600,
            percentOfTotal: 20,
            dueDate: "2024-07-01",
            status: "not_started",
          },
        ],
        workHistory: [
          {
            id: 5001,
            date: "2024-03-20",
            title: "Project Kickoff",
            description: "Initial meeting to discuss project requirements and timeline",
            status: "Completed",
            rating: 5
          },
          {
            id: 5002,
            date: "2024-04-03",
            title: "Design Phase Completion",
            description: "Completed and submitted website designs and wireframes",
            status: "Approved",
            rating: 5
          }
        ]
      },
      {
        id: 6,
        type: "milestone",
        title: "Mobile App Development",
        client: "Health & Fitness Co",
        clientContact: "Robert Johnson",
        startDate: "Apr 1, 2024",
        status: "Active",
        totalEarnings: 0,
        totalContract: 5000,
        milestones: [
          {
            id: 601,
            title: "App Design & Prototyping",
            description: "Create app design and interactive prototype for client approval.",
            amount: 1000,
            percentOfTotal: 20,
            dueDate: "2024-04-30",
            status: "pending_approval",
            submission: {
              date: "2024-04-28T18:20:00Z",
              description: "Completed app design and interactive prototype for all screens.",
              files: [
                {
                  name: "app-prototype.fig",
                  size: 8388608,
                  type: "application/octet-stream",
                },
                {
                  name: "design-assets.zip",
                  size: 15728640,
                  type: "application/zip",
                },
              ],
            },
          },
          {
            id: 602,
            title: "Core Functionality",
            description: "Develop the core features of the app including user authentication and profile management.",
            amount: 1500,
            percentOfTotal: 30,
            dueDate: "2024-06-15",
            status: "not_started",
          },
          {
            id: 603,
            title: "Advanced Features",
            description: "Implement advanced features like workout tracking, nutrition planning, and progress analytics.",
            amount: 1500,
            percentOfTotal: 30,
            dueDate: "2024-07-31",
            status: "not_started",
          },
          {
            id: 604,
            title: "Testing & Deployment",
            description: "Perform testing, bug fixes, and deploy to app stores.",
            amount: 1000,
            percentOfTotal: 20,
            dueDate: "2024-08-31",
            status: "not_started",
          },
        ],
        workHistory: [
          {
            id: 6001,
            date: "2024-04-01",
            title: "Project Kickoff",
            description: "Initial meeting to discuss app requirements and features",
            status: "Completed",
            rating: 5
          },
          {
            id: 6002,
            date: "2024-04-28",
            title: "Design Phase Completion",
            description: "Submitted app designs and interactive prototype",
            status: "Under Review",
            rating: null
          }
        ]
      },
    ];

    // Global state
    let currentJob = null;
    let isTimerRunning = false;
    let timerSeconds = 0;
    let timerInterval = null;
    let timerStartTime = null;
    let selectedMilestone = null;
    let currentCalendarView = 'weekly';
    let currentCalendarDate = new Date();
    let calendarEvents = [];
    let currentRating = 0;

    // DOM Elements
    const jobListView = document.getElementById('job-list-view');
    const dashboardView = document.getElementById('dashboard-view');
    const jobsGrid = document.getElementById('jobs-grid');
    const backButton = document.getElementById('back-button');
    const jobTitle = document.getElementById('job-title');
    const jobSubtitle = document.getElementById('job-subtitle');
    const statusLabel = document.getElementById('status-label');
    const statusValue = document.getElementById('status-value');
    const statusSubtitle = document.getElementById('status-subtitle');
    const earningsValue = document.getElementById('earnings-value');
    const earningsProgress = document.getElementById('earnings-progress');
    const earningsSubtitle = document.getElementById('earnings-subtitle');
    const rateLabel = document.getElementById('rate-label');
    const jobStatusBadge = document.getElementById('job-status-badge');

    // Dashboard views
    const timeTrackingDashboard = document.getElementById('time-tracking-dashboard');
    const oneTimeDashboard = document.getElementById('one-time-dashboard');
    const milestoneDashboard = document.getElementById('milestone-dashboard');

    // Time tracking elements
    const timerDisplay = document.getElementById('timer-display');
    const hoursUsed = document.getElementById('hours-used');
    const hoursProgress = document.getElementById('hours-progress');
    const timerButton = document.getElementById('timer-button');
    const manualEntryButton = document.getElementById('manual-entry-button');
    const timeLogsTable = document.getElementById('time-logs-table');
    const workHistoryContainer = document.getElementById('work-history-container');

    // Calendar elements
    const calendarTitle = document.getElementById('calendar-title');
    const prevPeriodButton = document.getElementById('prev-period');
    const nextPeriodButton = document.getElementById('next-period');
    const calendarViewOptions = document.querySelectorAll('.calendar-view-option');
    const weeklyView = document.getElementById('weekly-view');
    const monthlyView = document.getElementById('monthly-view');
    const yearlyView = document.getElementById('yearly-view');
    const weeklyCalendarBody = document.getElementById('weekly-calendar-body');
    const monthlyCalendarBody = document.getElementById('monthly-calendar-body');
    const yearlyCalendarGrid = document.getElementById('yearly-calendar-grid');

    // Dialog elements
    const manualEntryDialog = document.getElementById('manual-entry-dialog');
    const uploadProjectDialog = document.getElementById('upload-project-dialog');
    const contractDialog = document.getElementById('contract-dialog');
    const timeLogDialog = document.getElementById('time-log-dialog');
    const submitMilestoneDialog = document.getElementById('submit-milestone-dialog');
    const calendarEventDialog = document.getElementById('calendar-event-dialog');
    const rateWorkDialog = document.getElementById('rate-work-dialog');

    // Dialog buttons
    const cancelManualEntry = document.getElementById('cancel-manual-entry');
    const submitManualEntry = document.getElementById('submit-manual-entry');
    const cancelUpload = document.getElementById('cancel-upload');
    const submitUpload = document.getElementById('submit-upload');
    const closeContract = document.getElementById('close-contract');
    const closeTimeLog = document.getElementById('close-time-log');
    const cancelMilestone = document.getElementById('cancel-milestone');
    const submitMilestone = document.getElementById('submit-milestone');
    const closeCalendarEvent = document.getElementById('close-calendar-event');
    const cancelRating = document.getElementById('cancel-rating');
    const submitRating = document.getElementById('submit-rating');

    // One-time dashboard elements
    const deliverablesContainer = document.getElementById('deliverables-container');
    const viewContractButton = document.getElementById('view-contract-button');
    const uploadProjectButton = document.getElementById('upload-project-button');

    // Milestone dashboard elements
    const milestonesContainer = document.getElementById('milestones-container');
    const viewMilestoneContractButton = document.getElementById('view-milestone-contract-button');



    // Utility functions
    function formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);
    }

    function formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    }

    function formatFileSize(bytes) {
      if (bytes < 1024) return bytes + ' B';
      else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
      else return (bytes / 1048576).toFixed(1) + ' MB';
    }

    function formatTime(totalSeconds) {
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    function parseTimeToMinutes(timeString) {
      const [hours, minutes] = timeString.split(':').map(Number);
      return hours * 60 + minutes;
    }

    // Initialize the app
    function init() {
      renderJobList();
      setupEventListeners();
    }

    // Render the job list
    function renderJobList() {
      jobsGrid.innerHTML = '';

      jobsData.forEach(job => {
        const jobTypeLabel = getJobTypeLabel(job.type);
        const jobTypeBadgeVariant = getJobTypeBadgeVariant(job.type);

        const jobCard = document.createElement('div');
        jobCard.className = 'job-card';
        jobCard.innerHTML = `
          <div class="job-card-header">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="job-title">${job.title}</h3>
                <p class="job-subtitle">${job.client} • Started ${job.startDate}</p>
              </div>
              <span class="badge badge-${jobTypeBadgeVariant}">${jobTypeLabel}</span>
            </div>
          </div>
          <div class="job-card-content">
            <div class="job-info-item">
              <div class="job-info-label">
                <svg class="icon" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                <span>${job.type === "time-tracking" ? "Total Hours" : "Status"}</span>
              </div>
              <div class="job-info-value">
                ${job.type === "time-tracking" ? `${job.totalHours} / ${job.contractHours} hrs` : job.status}
              </div>
            </div>
            <div class="job-info-item">
              <div class="job-info-label">
                <svg class="icon" viewBox="0 0 24 24">
                  <line x1="12" y1="1" x2="12" y2="23"></line>
                  <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                </svg>
                <span>Earnings</span>
              </div>
              <div class="job-info-value">
                ${formatCurrency(job.totalEarnings)}
                ${job.type !== "one-time" ? `/${formatCurrency(job.totalContract)}` : ''}
              </div>
            </div>
            <div class="job-info-item">
              <div class="job-info-label">
                <svg class="icon" viewBox="0 0 24 24">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <span>Client</span>
              </div>
              <div class="job-info-value">${job.clientContact}</div>
            </div>
          </div>
          <div class="job-card-footer">
            <button class="button button-default button-full view-job-button" data-job-id="${job.id}">
              View Dashboard
            </button>
          </div>
        `;

        jobsGrid.appendChild(jobCard);
      });
    }

    // Set up event listeners
    function setupEventListeners() {

      // Job list view
      document.addEventListener('click', function(e) {
        if (e.target.classList.contains('view-job-button') || e.target.closest('.view-job-button')) {
          const button = e.target.classList.contains('view-job-button') ? e.target : e.target.closest('.view-job-button');
          const jobId = parseInt(button.dataset.jobId);
          const job = jobsData.find(j => j.id === jobId);
          if (job) {
            showJobDashboard(job);
          }
        }
      });

      // Back button
      backButton.addEventListener('click', showJobList);

      // Timer button
      timerButton.addEventListener('click', toggleTimer);

      // Manual entry button
      manualEntryButton.addEventListener('click', () => {
        document.getElementById('date').value = new Date().toISOString().split('T')[0];
        document.getElementById('timeIn').value = '';
        document.getElementById('timeOut').value = '';
        document.getElementById('notes').value = '';
        document.getElementById('proof').value = '';
        document.getElementById('proof-preview').classList.add('hidden');
        manualEntryDialog.classList.remove('hidden');
      });

      // Cancel manual entry
      cancelManualEntry.addEventListener('click', () => {
        manualEntryDialog.classList.add('hidden');
      });

      // Submit manual entry
      submitManualEntry.addEventListener('click', handleManualTimeSubmit);

      // Upload project button
      uploadProjectButton?.addEventListener('click', () => {
        document.getElementById('project-description').value = '';
        document.getElementById('project-files').value = '';
        document.getElementById('project-files-preview').classList.add('hidden');
        document.getElementById('project-files-preview').innerHTML = '';
        uploadProjectDialog.classList.remove('hidden');
      });

      // Cancel upload
      cancelUpload.addEventListener('click', () => {
        uploadProjectDialog.classList.add('hidden');
      });

      // Submit upload
      submitUpload.addEventListener('click', handleProjectUpload);

      // View contract button
      viewContractButton?.addEventListener('click', () => {
        showContract(currentJob);
      });

      // View milestone contract button
      viewMilestoneContractButton?.addEventListener('click', () => {
        showContract(currentJob);
      });

      // Close contract
      closeContract.addEventListener('click', () => {
        contractDialog.classList.add('hidden');
      });

      // Close time log
      closeTimeLog.addEventListener('click', () => {
        timeLogDialog.classList.add('hidden');
      });

      // Close calendar event
      closeCalendarEvent.addEventListener('click', () => {
        calendarEventDialog.classList.add('hidden');
      });

      // Cancel milestone
      cancelMilestone.addEventListener('click', () => {
        submitMilestoneDialog.classList.add('hidden');
      });

      // Submit milestone
      submitMilestone.addEventListener('click', handleMilestoneSubmit);

      // Calendar navigation
      prevPeriodButton.addEventListener('click', () => {
        navigateCalendar('prev');
      });

      nextPeriodButton.addEventListener('click', () => {
        navigateCalendar('next');
      });

      // Calendar view options
      calendarViewOptions.forEach(option => {
        option.addEventListener('click', () => {
          const view = option.dataset.view;
          setCalendarView(view);
        });
      });

      // Rating stars
      document.querySelectorAll('#rating-stars .rating-star').forEach(star => {
        star.addEventListener('click', function() {
          const rating = parseInt(this.dataset.rating);
          setRating(rating);
        });

        star.addEventListener('mouseover', function() {
          const rating = parseInt(this.dataset.rating);
          highlightStars(rating);
        });

        star.addEventListener('mouseout', function() {
          highlightStars(currentRating);
        });
      });

      // Cancel rating
      cancelRating?.addEventListener('click', () => {
        rateWorkDialog.classList.add('hidden');
        currentRating = 0;
        highlightStars(0);
      });

      // Submit rating
      submitRating?.addEventListener('click', handleRatingSubmit);

      // File upload previews
      document.getElementById('proof').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
          const previewUrl = URL.createObjectURL(file);
          document.getElementById('proof-image').src = previewUrl;
          document.getElementById('proof-preview').classList.remove('hidden');
        }
      });

      document.getElementById('project-files').addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
          const previewContainer = document.getElementById('project-files-preview');
          previewContainer.innerHTML = '';

          files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
              <svg class="icon mr-2 text-primary" viewBox="0 0 24 24">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
              <div class="file-item-info">
                <p class="file-item-name">${file.name}</p>
                <p class="file-item-size">${formatFileSize(file.size)}</p>
              </div>
            `;
            previewContainer.appendChild(fileItem);
          });

          previewContainer.classList.remove('hidden');
        }
      });

      document.getElementById('milestone-files')?.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
          const previewContainer = document.getElementById('milestone-files-preview');
          previewContainer.innerHTML = '';

          files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
              <svg class="icon mr-2 text-primary" viewBox="0 0 24 24">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
              <div class="file-item-info">
                <p class="file-item-name">${file.name}</p>
                <p class="file-item-size">${formatFileSize(file.size)}</p>
              </div>
            `;
            previewContainer.appendChild(fileItem);
          });

          previewContainer.classList.remove('hidden');
        }
      });

      // Tab switching
      document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', function() {
          const tabId = this.dataset.tab;

          // Update tab selection
          document.querySelectorAll('.tab').forEach(t => {
            t.setAttribute('aria-selected', t === this);
          });

          // Show/hide tab content
          document.querySelectorAll('[data-tab-content]').forEach(content => {
            if (content.dataset.tabContent === tabId) {
              content.classList.remove('hidden');

              // Initialize calendar if switching to calendar tab
              if (tabId === 'calendar') {
                updateCalendarEvents();
                renderCalendar();
              }

              // Initialize work history if switching to history tab
              if (tabId === 'history' && currentJob) {
                renderWorkHistory(currentJob.workHistory || []);
              }
            } else {
              content.classList.add('hidden');
            }
          });
        });
      });
    }

    // Show job dashboard
    function showJobDashboard(job) {
      currentJob = job;

      // Update job info
      jobTitle.textContent = job.title;
      jobSubtitle.textContent = `${job.client} • Started ${job.startDate}`;

      // Update status card (Total Hours)
      if (job.type === 'time-tracking') {
        document.querySelector('.dashboard-card:nth-child(1) .dashboard-card-title').textContent = 'Total Hours';
        document.querySelector('.dashboard-card:nth-child(1) .dashboard-card-value').textContent = `${job.totalHours} hrs`;
        document.querySelector('.dashboard-card:nth-child(1) .dashboard-card-subtitle').textContent = `Contract hours: ${job.contractHours} hrs`;
      } else {
        document.querySelector('.dashboard-card:nth-child(1) .dashboard-card-title').textContent = 'Status';
        document.querySelector('.dashboard-card:nth-child(1) .dashboard-card-value').textContent = job.status;
        document.querySelector('.dashboard-card:nth-child(1) .dashboard-card-subtitle').textContent = '';
      }

      // Update earnings card
      document.querySelector('.dashboard-card:nth-child(2) .dashboard-card-value').textContent = formatCurrency(job.totalEarnings);

      if (job.type !== 'one-time') {
        const progressPercentage = Math.round((job.totalEarnings / job.totalContract) * 100);
        document.querySelector('.dashboard-card:nth-child(2) .dashboard-progress-fill').style.width = `${progressPercentage}%`;
        document.querySelector('.dashboard-card:nth-child(2) .dashboard-card-subtitle').textContent =
          `${formatCurrency(job.totalEarnings)}/${formatCurrency(job.totalContract)} (${progressPercentage}%)`;
        document.querySelector('.dashboard-card:nth-child(2) .dashboard-card-progress').style.display = 'block';
      } else {
        document.querySelector('.dashboard-card:nth-child(2) .dashboard-card-progress').style.display = 'none';
        document.querySelector('.dashboard-card:nth-child(2) .dashboard-card-subtitle').textContent = '';
      }

      // Update contract rate card
      if (job.type === 'time-tracking') {
        document.querySelector('.dashboard-card:nth-child(3) .dashboard-card-title').textContent = 'Contract Rate';
        document.querySelector('.dashboard-card:nth-child(3) .dashboard-card-value').textContent = formatCurrency(job.totalContract);
      } else if (job.type === 'one-time') {
        document.querySelector('.dashboard-card:nth-child(3) .dashboard-card-title').textContent = 'Payment';
        document.querySelector('.dashboard-card:nth-child(3) .dashboard-card-value').textContent = formatCurrency(job.totalContract);
      } else {
        document.querySelector('.dashboard-card:nth-child(3) .dashboard-card-title').textContent = 'Payment';
        document.querySelector('.dashboard-card:nth-child(3) .dashboard-card-value').textContent = 'Per milestone';
      }

      document.querySelector('.dashboard-card:nth-child(3) .badge').textContent = job.status;

      // Show appropriate dashboard
      timeTrackingDashboard.classList.add('hidden');
      oneTimeDashboard.classList.add('hidden');
      milestoneDashboard.classList.add('hidden');

      if (job.type === 'time-tracking') {
        timeTrackingDashboard.classList.remove('hidden');
        renderTimeTrackingDashboard(job);
      } else if (job.type === 'one-time') {
        oneTimeDashboard.classList.remove('hidden');
        renderOneTimeDashboard(job);
      } else if (job.type === 'milestone') {
        milestoneDashboard.classList.remove('hidden');
        renderMilestoneDashboard(job);
      }

      // Switch views
      jobListView.classList.add('hidden');
      dashboardView.classList.remove('hidden');
    }

    // Show job list
    function showJobList() {
      // Stop timer if running
      if (isTimerRunning) {
        toggleTimer();
      }

      // Reset timer
      timerSeconds = 0;
      timerDisplay.textContent = formatTime(timerSeconds);

      // Switch views
      dashboardView.classList.add('hidden');
      jobListView.classList.remove('hidden');

      // Clear current job
      currentJob = null;
    }

    // Render time tracking dashboard
    function renderTimeTrackingDashboard(job) {
      // Update timer display
      timerDisplay.textContent = formatTime(timerSeconds);

      // Update hours info
      hoursUsed.textContent = `${job.totalHours.toFixed(2)} / ${job.contractHours} hours used`;
      hoursProgress.style.width = `${(job.totalHours / job.contractHours) * 100}%`;

      // Render time logs
      renderTimeLogs(job.timeLogs);

      // Render work history
      renderWorkHistory(job.workHistory || []);

      // Update calendar events
      updateCalendarEvents();
    }

    // Render work history
    function renderWorkHistory(history) {
      const container = workHistoryContainer;

  if (!history || history.length === 0) {
    container.innerHTML = `
      <div class="text-center py-6">
        <svg class="icon-xl mx-auto text-muted-foreground mb-4" viewBox="0 0 24 24">
          <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h4 class="text-lg font-medium mb-2">No work history yet</h4>
        <p class="text-sm text-muted-foreground">Your work history will appear here</p>
      </div>
    `;
    return;
  }

  container.innerHTML = '<div class="content-grid">';

  history.forEach(item => {
    const historyItem = document.createElement('div');
    historyItem.className = 'border rounded-lg p-4';

    let ratingHtml = '';
    if (item.rating !== null) {
      ratingHtml = `
        <div class="history-rating mt-2">
          <div class="rating">
            ${generateRatingStars(item.rating)}
          </div>
        </div>
      `;
    }

    historyItem.innerHTML = `
      <div class="history-date">${formatDate(item.date)}</div>
      <h4 class="history-title font-medium mt-1">${item.title}</h4>
      <p class="text-sm mb-2">${item.description}</p>
      <div class="flex justify-between items-center">
        <span class="history-status badge ${getStatusBadgeClass(item.status)}">${item.status}</span>
      </div>
      ${ratingHtml}
    `;

    container.querySelector('.content-grid').appendChild(historyItem);
  });

  container.querySelector('.content-grid').innerHTML += '</div>';
}

    // Generate rating stars
    function generateRatingStars(rating) {
      let starsHtml = '';
      for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
          starsHtml += `<svg class="icon rating-star filled" viewBox="0 0 24 24"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>`;
        } else {
          starsHtml += `<svg class="icon rating-star" viewBox="0 0 24 24"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>`;
        }
      }
      return starsHtml;
    }

    // Set rating
    function setRating(rating) {
      currentRating = rating;
      highlightStars(rating);
    }

    // Highlight stars
    function highlightStars(rating) {
      document.querySelectorAll('#rating-stars .rating-star').forEach((star, index) => {
        if (index < rating) {
          star.classList.add('filled');
        } else {
          star.classList.remove('filled');
        }
      });
    }

    // Handle rating submit
    function handleRatingSubmit() {
      if (currentRating === 0) {
        alert('Please select a rating');
        return;
      }

      const comments = document.getElementById('rating-comments').value;

      // Here you would typically send the rating to your backend
      console.log(`Rating submitted: ${currentRating}, Comments: ${comments}`);

      // Close dialog
      rateWorkDialog.classList.add('hidden');

      // Reset rating
      currentRating = 0;
      highlightStars(0);
    }

    // Get status badge class
    function getStatusBadgeClass(status) {
      switch (status.toLowerCase()) {
        case 'completed':
          return 'badge-primary';
        case 'in progress':
          return 'badge-secondary';
        case 'approved':
          return 'badge-primary';
        case 'under review':
          return 'badge-accent';
        default:
          return 'badge-outline';
      }
    }

    // Render time logs
    function renderTimeLogs(timeLogs) {
      const tbody = timeLogsTable.querySelector('tbody');
      tbody.innerHTML = '';

      if (timeLogs.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td colspan="5" class="p-4 text-center text-muted-foreground">
            No time logs recorded yet
          </td>
        `;
        tbody.appendChild(tr);
        return;
      }

      timeLogs.forEach(log => {
        const tr = document.createElement('tr');
        tr.className = 'border-b transition-colors hover:bg-muted/50 cursor-pointer';
        tr.dataset.logId = log.id;

        tr.innerHTML = `
          <td class="p-4 align-middle">${formatDate(log.date)}</td>
          <td class="p-4 align-middle">${log.timeIn.substring(0, 5)}</td>
          <td class="p-4 align-middle">${log.timeOut.substring(0, 5)}</td>
          <td class="p-4 align-middle">${log.duration.substring(0, 5)}</td>
          <td class="p-4 align-middle">
            ${log.isManual
              ? '<span class="badge" style="background-color: #fef3c7; color: #92400e;">Manual</span>'
              : '<span class="badge" style="background-color: #d1fae5; color: #065f46;">Automatic</span>'
            }
          </td>
        `;

        tr.addEventListener('click', () => {
          showTimeLogDetails(log);
        });

        tbody.appendChild(tr);
      });
    }

    // Show time log details
    function showTimeLogDetails(log) {
      const detailsContainer = document.getElementById('time-log-details');

      detailsContainer.innerHTML = `
        <div class="grid grid-cols-2 gap-4">
          <div>
            <h4 class="text-sm font-medium text-muted-foreground">Date</h4>
            <p>${formatDate(log.date)}</p>
          </div>
          <div>
            <h4 class="text-sm font-medium text-muted-foreground">Duration</h4>
            <p>${log.duration.substring(0, 5)}</p>
          </div>
          <div>
            <h4 class="text-sm font-medium text-muted-foreground">Time In</h4>
            <p>${log.timeIn.substring(0, 5)}</p>
          </div>
          <div>
            <h4 class="text-sm font-medium text-muted-foreground">Time Out</h4>
            <p>${log.timeOut.substring(0, 5)}</p>
          </div>
        </div>

        ${log.isManual ? `
          <div class="mt-4">
            <h4 class="text-sm font-medium text-muted-foreground">Explanation</h4>
            <p class="mt-1 text-sm">${log.notes}</p>
          </div>

          ${log.proofName ? `
            <div class="mt-4">
              <h4 class="text-sm font-medium text-muted-foreground">Proof</h4>
              <div class="mt-2 p-2 bg-muted rounded-md flex items-center">
                <svg class="icon mr-2 text-primary" viewBox="0 0 24 24">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
                <span class="text-sm">${log.proofName}</span>
              </div>
            </div>
          ` : ''}
        ` : ''}
      `;

      timeLogDialog.classList.remove('hidden');
    }

    // Toggle timer
    function toggleTimer() {
      if (isTimerRunning) {
        // Stop timer
        clearInterval(timerInterval);
        isTimerRunning = false;
        timerButton.innerHTML = `
          <svg class="icon mr-2" viewBox="0 0 24 24">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
          Start
        `;
        timerButton.classList.remove('button-destructive');

        // Calculate duration
        const now = new Date();
        const duration = Math.round((now - timerStartTime) / 1000); // in seconds
        const durationHours = duration / 3600;

        // Check if adding this time would exceed contract hours
        const newTotalHours = currentJob.totalHours + durationHours;

        if (newTotalHours > currentJob.contractHours) {
          alert(`You've reached your contract limit of ${currentJob.contractHours} hours. Please submit a manual time entry with explanation.`);
          return;
        }

        // Format times
        const timeIn = timerStartTime.toTimeString().split(' ')[0];
        const timeOut = now.toTimeString().split(' ')[0];
        const date = now.toISOString().split('T')[0];

        // Add time log
        const newLog = {
          id: Date.now(),
          date,
          timeIn,
          timeOut,
          duration: formatTime(duration),
          isManual: false,
        };

        // Update job data
        currentJob.totalHours = newTotalHours;
        currentJob.totalEarnings = currentJob.totalEarnings + durationHours * currentJob.rate;
        currentJob.timeLogs = [newLog, ...currentJob.timeLogs];

        // Update calendar with the new time log
        calendarEvents.push({
          id: newLog.id,
          date,
          timeIn,
          timeOut,
          duration: formatTime(duration),
          isManual: false,
          title: currentJob.title
        });

        // Update UI
        renderTimeTrackingDashboard(currentJob);

        // Update job in jobsData
        const jobIndex = jobsData.findIndex(j => j.id === currentJob.id);
        if (jobIndex !== -1) {
          jobsData[jobIndex] = currentJob;
        }

        // Reset timer
        timerSeconds = 0;
      } else {
        // Check if starting would exceed contract hours
        if (currentJob.totalHours >= currentJob.contractHours) {
          alert(`You've reached your contract limit of ${currentJob.contractHours} hours. Please submit a manual time entry with explanation.`);
          return;
        }

        // Start timer
        timerStartTime = new Date();
        isTimerRunning = true;
        timerButton.innerHTML = `
          <svg class="icon mr-2" viewBox="0 0 24 24">
            <rect x="6" y="4" width="4" height="16"></rect>
            <rect x="14" y="4" width="4" height="16"></rect>
          </svg>
          Stop
        `;
        timerButton.classList.add('button-destructive');

        timerInterval = setInterval(() => {
          timerSeconds++;
          timerDisplay.textContent = formatTime(timerSeconds);
        }, 1000);
      }
    }

    // Handle manual time submit
    function handleManualTimeSubmit() {
      const date = document.getElementById('date').value;
      const timeIn = document.getElementById('timeIn').value;
      const timeOut = document.getElementById('timeOut').value;
      const notes = document.getElementById('notes').value;
      const proofFile = document.getElementById('proof').files[0];

      if (!date || !timeIn || !timeOut || !notes || !proofFile) {
        alert('Please fill in all fields and upload proof');
        return;
      }

      // Calculate duration
      const inParts = timeIn.split(':').map(Number);
      const outParts = timeOut.split(':').map(Number);

      let totalMinutes = outParts[0] * 60 + outParts[1] - (inParts[0] * 60 + inParts[1]);
      if (totalMinutes < 0) totalMinutes += 24 * 60; // Add a day if timeOut is on the next day

      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      const duration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;

      const durationHours = hours + minutes / 60;

      // Create new log
      const newLog = {
        id: Date.now(),
        date,
        timeIn: `${timeIn}:00`,
        timeOut: `${timeOut}:00`,
        duration,
        isManual: true,
        notes,
        proofName: proofFile.name,
      };

      // Update job data
      currentJob.totalHours = currentJob.totalHours + durationHours;
      currentJob.totalEarnings = currentJob.totalEarnings + durationHours * currentJob.rate;
      currentJob.timeLogs = [newLog, ...currentJob.timeLogs];

      // Update calendar with the new manual time log
      calendarEvents.push({
        id: newLog.id,
        date,
        timeIn: `${timeIn}:00`,
        timeOut: `${timeOut}:00`,
        duration,
        isManual: true,
        title: currentJob.title
      });

      // Update UI
      renderTimeTrackingDashboard(currentJob);

      // Update job in jobsData
      const jobIndex = jobsData.findIndex(j => j.id === currentJob.id);
      if (jobIndex !== -1) {
        jobsData[jobIndex] = currentJob;
      }

      // Close dialog
      manualEntryDialog.classList.add('hidden');
    }

    // Render one-time dashboard
    function renderOneTimeDashboard(job) {
  const container = document.getElementById('deliverables-container');

  if (!job.deliverables || job.deliverables.length === 0) {
    container.innerHTML = `
      <div class="text-center py-12">
        <svg class="icon-xl mx-auto text-muted-foreground mb-4" viewBox="0 0 24 24">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10 9 9 9 8 9"></polyline>
        </svg>
        <h4 class="text-lg font-medium mb-2">No deliverables yet</h4>
        <p class="text-sm text-muted-foreground mb-6">Upload your completed work to fulfill this project</p>
      </div>
    `;
    return;
  }

  let html = '<div class="content-grid">';

  job.deliverables.forEach(deliverable => {
    html += `
      <div class="border rounded-lg p-4">
        <div class="flex justify-between items-start mb-2">
          <div>
            <p class="text-sm text-muted-foreground">Uploaded on ${formatDate(deliverable.date)}</p>
            <p class="mt-2">${deliverable.description}</p>
          </div>
          <svg class="icon text-green-500" viewBox="0 0 24 24">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </div>
        <div class="mt-4 grid grid-cols-1 gap-2">
    `;

    deliverable.files.forEach(file => {
      html += `
        <div class="file-item">
          <svg class="icon mr-2 text-primary" viewBox="0 0 24 24">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
          <div class="file-item-info">
            <p class="file-item-name">${file.name}</p>
            <p class="file-item-size">${formatFileSize(file.size)}</p>
          </div>
          <button class="button button-outline button-icon">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
          </button>
        </div>
      `;
    });

    html += `
        </div>
      </div>
    `;
  });

  html += "</div>";
  container.innerHTML = html;
}

    // Handle project upload
    function handleProjectUpload() {
      const description = document.getElementById('project-description').value;
      const files = Array.from(document.getElementById('project-files').files);

      if (files.length === 0) {
        alert('Please select at least one file to upload');
        return;
      }

      // Create new deliverable
      const newDeliverable = {
        id: Date.now(),
        date: new Date().toISOString(),
        description,
        files: files.map(file => ({
          name: file.name,
          size: file.size,
          type: file.type,
        })),
      };

      // Update job data
      if (!currentJob.deliverables) {
        currentJob.deliverables = [];
      }

      currentJob.deliverables = [newDeliverable, ...currentJob.deliverables];

      // Add to work history
      if (!currentJob.workHistory) {
        currentJob.workHistory = [];
      }

      const newHistoryItem = {
        id: Date.now(),
        date: new Date().toISOString().split('T')[0],
        title: "Project Submission",
        description: description || "Submitted project deliverables",
        status: "Under Review",
        rating: null
      };

      currentJob.workHistory = [newHistoryItem, ...currentJob.workHistory];

      // Update UI
      renderOneTimeDashboard(currentJob);

      // Update job in jobsData
      const jobIndex = jobsData.findIndex(j => j.id === currentJob.id);
      if (jobIndex !== -1) {
        jobsData[jobIndex] = currentJob;
      }

      // Close dialog
      uploadProjectDialog.classList.add('hidden');

      // Show rating dialog if available
      if (rateWorkDialog) {
        rateWorkDialog.classList.remove('hidden');
      }
    }

    // Render milestone dashboard
    function renderMilestoneDashboard(job) {
      const container = document.getElementById('milestones-container');
      container.innerHTML = '';

      job.milestones.forEach(milestone => {
        const milestoneEl = document.createElement('div');
        milestoneEl.className = 'border rounded-lg overflow-hidden';

        const statusBadge = getMilestoneStatusBadge(milestone.status);
        const statusIcon = getMilestoneStatusIcon(milestone.status);

        milestoneEl.innerHTML = `
          <div class="bg-muted p-4 flex justify-between items-center">
            <div>
              <h4 class="font-medium">${milestone.title}</h4>
              <p class="text-sm text-muted-foreground">Due: ${formatDate(milestone.dueDate)}</p>
            </div>
            <div class="flex items-center gap-3">
              ${statusBadge}
              <div class="text-right">
                <div class="font-medium">$${milestone.amount}</div>
                <div class="text-xs text-muted-foreground">${milestone.percentOfTotal}% of total</div>
              </div>
            </div>
          </div>

          <div class="p-4">
            <p class="mb-4">${milestone.description}</p>

            ${milestone.submission ? `
              <div class="mt-4 border-t pt-4">
                <div class="flex justify-between items-start mb-2">
                  <div>
                    <p class="text-sm font-medium">Submission</p>
                    <p class="text-xs text-muted-foreground">Submitted on ${formatDate(milestone.submission.date)}</p>
                    <p class="mt-2 text-sm">${milestone.submission.description}</p>
                  </div>
                  ${statusIcon}
                </div>

                <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-2">
                  ${milestone.submission.files.map(file => `
                    <div class="file-item">
                      <svg class="icon mr-2 text-primary" viewBox="0 0 24 24">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                      </svg>
                      <div class="file-item-info">
                        <p class="file-item-name">${file.name}</p>
                        <p class="file-item-size">${formatFileSize(file.size)}</p>
                      </div>
                      <button class="button button-outline button-icon">
                        <svg class="icon" viewBox="0 0 24 24">
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                          <polyline points="7 10 12 15 17 10"></polyline>
                          <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                      </button>
                    </div>
                  `).join('')}
                </div>

                ${milestone.status === "rejected" && milestone.feedback ? `
                  <div class="mt-4 p-3 bg-red-50 border border-red-100 rounded-md">
                    <p class="text-sm font-medium text-red-800">Client Feedback:</p>
                    <p class="text-sm text-red-700 mt-1">${milestone.feedback}</p>
                  </div>
                ` : ''}
              </div>
            ` : ''}
          </div>

          <div class="bg-muted/50 p-3 border-t flex justify-end">
            <button class="button ${milestone.status === "approved" ? "button-outline" : "button-default"} submit-milestone-button"
                    data-milestone-id="${milestone.id}"
                    ${milestone.status === "approved" ? "disabled" : ""}>
              <svg class="icon mr-2" viewBox="0 0 24 24">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="17 8 12 3 7 8"></polyline>
                <line x1="12" y1="3" x2="12" y2="15"></line>
              </svg>
              ${milestone.submission ? "Update Submission" : "Submit Milestone"}
            </button>
          </div>
        `;

        // Add event listener to submit button
        const submitButton = milestoneEl.querySelector('.submit-milestone-button');
        submitButton.addEventListener('click', () => {
          if (milestone.status === "approved") {
            alert("This milestone has already been approved.");
            return;
          }

          selectedMilestone = milestone;
          document.getElementById('milestone-title').textContent = `Submit Milestone: ${milestone.title}`;
          document.getElementById('milestone-description').value = '';
          document.getElementById('milestone-files').value = '';
          document.getElementById('milestone-files-preview').classList.add('hidden');
          document.getElementById('milestone-files-preview').innerHTML = '';
          submitMilestoneDialog.classList.remove('hidden');
        });

        container.appendChild(milestoneEl);
      });
    }

    // Handle milestone submit
    function handleMilestoneSubmit() {
      if (!selectedMilestone) return;

      const description = document.getElementById('milestone-description').value;
      const files = Array.from(document.getElementById('milestone-files').files);

      if (files.length === 0) {
        alert('Please select at least one file to upload');
        return;
      }

      // Create submission
      const submission = {
        date: new Date().toISOString(),
        description,
        files: files.map(file => ({
          name: file.name,
          size: file.size,
          type: file.type,
        })),
      };

      // Update milestone
      const updatedMilestones = currentJob.milestones.map(milestone => {
        if (milestone.id === selectedMilestone.id) {
          return {
            ...milestone,
            status: "pending_approval",
            submission,
          };
        }
        return milestone;
      });

      // Update job data
      currentJob.milestones = updatedMilestones;

      // Add to work history
      if (!currentJob.workHistory) {
        currentJob.workHistory = [];
      }

      const newHistoryItem = {
        id: Date.now(),
        date: new Date().toISOString().split('T')[0],
        title: selectedMilestone.title,
        description: description || `Submitted milestone: ${selectedMilestone.title}`,
        status: "Under Review",
        rating: null
      };

      currentJob.workHistory = [newHistoryItem, ...currentJob.workHistory];

      // Update UI
      renderMilestoneDashboard(currentJob);

      // Update job in jobsData
      const jobIndex = jobsData.findIndex(j => j.id === currentJob.id);
      if (jobIndex !== -1) {
        jobsData[jobIndex] = currentJob;
      }

      // Close dialog
      submitMilestoneDialog.classList.add('hidden');
      selectedMilestone = null;

      // Show rating dialog if available
      if (rateWorkDialog) {
        rateWorkDialog.classList.remove('hidden');
      }
    }

    // Show contract
    function showContract(job) {
      document.getElementById('contract-title').textContent = job.title;
      document.getElementById('contract-subtitle').textContent = `Contract with ${job.client}`;

      if (job.type === 'one-time') {
        document.getElementById('contract-scope').textContent = 'This contract outlines the one-time project deliverables as agreed upon by both parties. The freelancer agrees to complete all work as specified in the project requirements.';
        document.getElementById('contract-payment').textContent = `Payment of ${formatCurrency(job.totalContract)} will be made upon satisfactory completion and delivery of all project requirements. Payment will be processed within 7 days of client approval.`;
        document.getElementById('contract-ip').textContent = 'Upon full payment, all intellectual property rights for the deliverables will transfer to the client. The freelancer retains the right to showcase the work in their portfolio unless otherwise specified.';
        document.getElementById('contract-milestones-section').classList.add('hidden');
      } else if (job.type === 'milestone') {
        document.getElementById('contract-scope').textContent = 'This contract outlines the milestone-based deliverables as agreed upon by both parties. The freelancer agrees to complete all work as specified in each milestone.';
        document.getElementById('contract-payment').textContent = `Payment will be made upon successful completion and approval of each milestone. Total contract rate is ${formatCurrency(job.totalContract)}, divided across milestones as specified.`;
        document.getElementById('contract-ip').textContent = 'Upon full payment of each milestone, intellectual property rights for the respective deliverables will transfer to the client. The freelancer retains the right to showcase the work in their portfolio unless otherwise specified.';

        // Render milestones
        const milestonesContainer = document.getElementById('contract-milestones');
        milestonesContainer.innerHTML = '';

        job.milestones.forEach(milestone => {
          const milestoneEl = document.createElement('div');
          milestoneEl.className = 'border-b pb-2';
          milestoneEl.innerHTML = `
            <div class="flex justify-between">
              <span class="text-sm font-medium">${milestone.title}</span>
              <span class="text-sm">${formatCurrency(milestone.amount)}</span>
            </div>
            <p class="text-xs text-muted-foreground">Due: ${formatDate(milestone.dueDate)}</p>
          `;
          milestonesContainer.appendChild(milestoneEl);
        });

        document.getElementById('contract-milestones-section').classList.remove('hidden');
      } else {
        document.getElementById('contract-scope').textContent = 'This contract outlines the hourly work arrangement as agreed upon by both parties. The freelancer agrees to track time accurately and provide regular updates on progress.';
        document.getElementById('contract-payment').textContent = `Payment will be made at a rate of ${formatCurrency(job.rate)} per hour, up to a maximum of ${job.contractHours} hours (${formatCurrency(job.totalContract)} total). Additional hours require prior approval.`;
        document.getElementById('contract-ip').textContent = 'Upon payment for hours worked, intellectual property rights for the work completed during those hours will transfer to the client. The freelancer retains the right to showcase the work in their portfolio unless otherwise specified.';
        document.getElementById('contract-milestones-section').classList.add('hidden');
      }

      contractDialog.classList.remove('hidden');
    }

    // Calendar functions
    function updateCalendarEvents() {
      calendarEvents = [];

      if (currentJob && currentJob.timeLogs) {
        currentJob.timeLogs.forEach(log => {
          calendarEvents.push({
            id: log.id,
            date: log.date,
            timeIn: log.timeIn,
            timeOut: log.timeOut,
            duration: log.duration,
            isManual: log.isManual,
            title: currentJob.title
          });
        });
      }

      renderCalendar();
    }

    function setCalendarView(view) {
      // Update active view option
      calendarViewOptions.forEach(option => {
        if (option.dataset.view === view) {
          option.classList.add('active');
        } else {
          option.classList.remove('active');
        }
      });

      // Hide all views
      weeklyView.classList.add('hidden');
      monthlyView.classList.add('hidden');
      yearlyView.classList.add('hidden');

      // Show selected view
      if (view === 'weekly') {
        weeklyView.classList.remove('hidden');
        currentCalendarView = 'weekly';
      } else if (view === 'monthly') {
        monthlyView.classList.remove('hidden');
        currentCalendarView = 'monthly';
      } else if (view === 'yearly') {
        yearlyView.classList.remove('hidden');
        currentCalendarView = 'yearly';
      }

      renderCalendar();
    }

    function navigateCalendar(direction) {
      if (currentCalendarView === 'weekly') {
        // Navigate by week
        const days = direction === 'prev' ? -7 : 7;
        currentCalendarDate.setDate(currentCalendarDate.getDate() + days);
      } else if (currentCalendarView === 'monthly') {
        // Navigate by month
        const months = direction === 'prev' ? -1 : 1;
        currentCalendarDate.setMonth(currentCalendarDate.getMonth() + months);
      } else if (currentCalendarView === 'yearly') {
        // Navigate by year
        const years = direction === 'prev' ? -1 : 1;
        currentCalendarDate.setFullYear(currentCalendarDate.getFullYear() + years);
      }

      renderCalendar();
    }

    function renderCalendar() {
      if (currentCalendarView === 'weekly') {
        renderWeeklyCalendar();
      } else if (currentCalendarView === 'monthly') {
        renderMonthlyCalendar();
      } else if (currentCalendarView === 'yearly') {
        renderYearlyCalendar();
      }
    }

    function renderWeeklyCalendar() {
      // Get the start of the week (Sunday)
      const startOfWeek = new Date(currentCalendarDate);
      startOfWeek.setDate(currentCalendarDate.getDate() - currentCalendarDate.getDay());

      // Get the end of the week (Saturday)
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);

      // Update calendar title
      calendarTitle.textContent = `${formatDate(startOfWeek)} - ${formatDate(endOfWeek)}`;

      // Update header row with day names and dates
      const headerRow = weeklyView.querySelector('thead tr');
      headerRow.innerHTML = '<th></th>';

      for (let i = 0; i < 7; i++) {
        const day = new Date(startOfWeek);
        day.setDate(startOfWeek.getDate() + i);

        const dayName = day.toLocaleDateString('en-US', { weekday: 'short' });
        const dayNumber = day.getDate();

        headerRow.innerHTML += `<th>${dayName} ${dayNumber}</th>`;
      }

      // Generate time slots
      weeklyCalendarBody.innerHTML = '';

      // Create rows for each hour from 8:00 to 19:00
      for (let hour = 8; hour <= 19; hour++) {
        const row = document.createElement('tr');

        // Add time column
        const timeCell = document.createElement('td');
        timeCell.className = 'time-column';
        timeCell.textContent = `${hour.toString().padStart(2, '0')}:00`;
        row.appendChild(timeCell);

        // Add cells for each day
        for (let day = 0; day < 7; day++) {
          const date = new Date(startOfWeek);
          date.setDate(startOfWeek.getDate() + day);

          const cell = document.createElement('td');
          cell.dataset.date = date.toISOString().split('T')[0];
          cell.dataset.hour = hour;

          row.appendChild(cell);
        }

        weeklyCalendarBody.appendChild(row);
      }

      // Add events to the calendar
      calendarEvents.forEach(event => {
        const eventDate = new Date(event.date);
        const dayIndex = eventDate.getDay(); // 0 = Sunday, 6 = Saturday

        // Check if the event is in the current week
        const eventStartOfWeek = new Date(eventDate);
        eventStartOfWeek.setDate(eventDate.getDate() - eventDate.getDay());

        if (eventStartOfWeek.getTime() === startOfWeek.getTime()) {
          // Parse time
          const timeInParts = event.timeIn.split(':').map(Number);
          const timeOutParts = event.timeOut.split(':').map(Number);

          const startHour = timeInParts[0];
          const startMinute = timeInParts[1];
          const endHour = timeOutParts[0];
          const endMinute = timeOutParts[1];

          // Only show events that fall within our display hours (8:00 - 19:00)
          if (startHour >= 8 && startHour < 19) {
            // Find the cell for this event
            const dayColumn = dayIndex + 1; // +1 because the first column is the time

            // Calculate position and height
            const startFromTop = (startHour - 8) * 3 + (startMinute / 60) * 3; // 3rem per hour
            const durationInHours = (endHour - startHour) + (endMinute - startMinute) / 60;
            const height = durationInHours * 3; // 3rem per hour

            // Create event element
            const eventEl = document.createElement('div');
            eventEl.className = `calendar-event ${event.isManual ? 'manual' : ''}`;
            eventEl.style.top = `${startFromTop}rem`;
            eventEl.style.height = `${height}rem`;
            eventEl.dataset.eventId = event.id;

            eventEl.innerHTML = `
              <div class="calendar-event-title">${event.title || 'Work Session'}</div>
              <div class="calendar-event-time">${event.timeIn.substring(0, 5)} - ${event.timeOut.substring(0, 5)}</div>
            `;

            // Add event listener
            eventEl.addEventListener('click', () => {
              showCalendarEventDetails(event);
            });

            // Find the cell and append the event
            const cells = weeklyCalendarBody.querySelectorAll('tr');
            const row = cells[startHour - 8]; // -8 because we start at 8:00

            if (row) {
              const cell = row.children[dayColumn];
              if (cell) {
                cell.style.position = 'relative';
                cell.appendChild(eventEl);
              }
            }
          }
        }
      });
    }

    // Update the renderMonthlyCalendar function to show time in/out
function renderMonthlyCalendar() {
  // Get the first day of the month
  const firstDay = new Date(currentCalendarDate.getFullYear(), currentCalendarDate.getMonth(), 1);

  // Get the last day of the month
  const lastDay = new Date(currentCalendarDate.getFullYear(), currentCalendarDate.getMonth() + 1, 0);

  // Update calendar title
  calendarTitle.textContent = firstDay.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

  // Clear the monthly calendar body
  monthlyCalendarBody.innerHTML = '';

  // Get the day of the week for the first day of the month (0 = Sunday, 6 = Saturday)
  const firstDayOfWeek = firstDay.getDay();

  // Create rows for the calendar
  let date = new Date(firstDay);
  date.setDate(1 - firstDayOfWeek); // Start from the Sunday before the first day of the month

  // Create 6 rows (max number of weeks in a month)
  for (let week = 0; week < 6; week++) {
    const row = document.createElement('tr');

    // Create 7 cells for each day of the week
    for (let day = 0; day < 7; day++) {
      const cell = document.createElement('td');

      // Check if the date is in the current month
      const isCurrentMonth = date.getMonth() === currentCalendarDate.getMonth();

      // Check if the date is today
      const isToday = date.toDateString() === new Date().toDateString();

      // Add appropriate classes
      if (!isCurrentMonth) {
        cell.classList.add('other-month');
      }

      if (isToday) {
        cell.classList.add('today');
      }

      // Add date number
      const dayNumber = document.createElement('div');
      dayNumber.className = 'day-number';
      dayNumber.textContent = date.getDate();
      cell.appendChild(dayNumber);

      // Add container for events
      const eventsContainer = document.createElement('div');
      eventsContainer.className = 'day-events';
      cell.appendChild(eventsContainer);

      // Add events for this day
      const dateString = date.toISOString().split('T')[0];
      const dayEvents = calendarEvents.filter(event => event.date === dateString);

      dayEvents.forEach(event => {
        const eventEl = document.createElement('div');
        eventEl.className = `day-event ${event.isManual ? 'manual' : ''}`;
        eventEl.dataset.eventId = event.id;
        eventEl.textContent = `${event.timeIn.substring(0, 5)} - ${event.timeOut.substring(0, 5)}`;

        // Add event listener
        eventEl.addEventListener('click', () => {
          showCalendarEventDetails(event);
        });

        eventsContainer.appendChild(eventEl);
      });

      row.appendChild(cell);

      // Move to the next day
      date.setDate(date.getDate() + 1);
    }

    monthlyCalendarBody.appendChild(row);

    // Stop if we've gone past the end of the month
    if (date.getMonth() !== currentCalendarDate.getMonth() && date.getDay() === 0) {
      break;
    }
  }
}

// Update the renderYearlyCalendar function to show time records
function renderYearlyCalendar() {
  // Update calendar title
  calendarTitle.textContent = currentCalendarDate.getFullYear().toString();

  // Clear the yearly calendar grid
  yearlyCalendarGrid.innerHTML = '';

  // Create a mini calendar for each month
  for (let month = 0; month < 12; month++) {
    const monthDate = new Date(currentCalendarDate.getFullYear(), month, 1);
    const monthName = monthDate.toLocaleDateString('en-US', { month: 'long' });

    // Create month container
    const monthContainer = document.createElement('div');
    monthContainer.className = 'month';

    // Create month header
    const monthHeader = document.createElement('div');
    monthHeader.className = 'month-header';
    monthHeader.textContent = monthName;
    monthContainer.appendChild(monthHeader);

    // Create month grid
    const monthGrid = document.createElement('div');
    monthGrid.className = 'month-grid';

    // Add weekday headers
    const weekdays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
    weekdays.forEach(day => {
      const weekdayEl = document.createElement('div');
      weekdayEl.className = 'weekday';
      weekdayEl.textContent = day;
      monthGrid.appendChild(weekdayEl);
    });

    // Get the first day of the month
    const firstDay = new Date(currentCalendarDate.getFullYear(), month, 1);
    const firstDayOfWeek = firstDay.getDay();

    // Get the last day of the month
    const lastDay = new Date(currentCalendarDate.getFullYear(), month + 1, 0);
    const daysInMonth = lastDay.getDate();

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfWeek; i++) {
      const emptyDay = document.createElement('div');
      emptyDay.className = 'day other-month';
      monthGrid.appendChild(emptyDay);
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentCalendarDate.getFullYear(), month, day);
      const dateString = date.toISOString().split('T')[0];

      // Check if there are events on this day
      const dayEvents = calendarEvents.filter(event => event.date === dateString);
      const hasEvents = dayEvents.length > 0;

      const dayEl = document.createElement('div');
      dayEl.className = `day ${hasEvents ? 'has-events' : ''}`;
      dayEl.textContent = day;

      // Add event listener to show events or navigate to monthly view
      dayEl.addEventListener('click', () => {
        if (hasEvents) {
          // Show the first event's details
          showCalendarEventDetails(dayEvents[0]);
        } else {
          // Navigate to this date in monthly view
          currentCalendarDate = new Date(date);
          setCalendarView('monthly');
        }
      });

      monthGrid.appendChild(dayEl);
    }

    // Add empty cells for days after the last day of the month
    const lastDayOfWeek = lastDay.getDay();
    for (let i = lastDayOfWeek + 1; i < 7; i++) {
      const emptyDay = document.createElement('div');
      emptyDay.className = 'day other-month';
      monthGrid.appendChild(emptyDay);
    }

    monthContainer.appendChild(monthGrid);
    yearlyCalendarGrid.appendChild(monthContainer);
  }
}

// Update the renderOneTimeDashboard function to use grid layout
function renderOneTimeDashboard(job) {
  const container = document.getElementById('deliverables-container');

  if (!job.deliverables || job.deliverables.length === 0) {
    container.innerHTML = `
      <div class="text-center py-12">
        <svg class="icon-xl mx-auto text-muted-foreground mb-4" viewBox="0 0 24 24">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10 9 9 9 8 9"></polyline>
        </svg>
        <h4 class="text-lg font-medium mb-2">No deliverables yet</h4>
        <p class="text-sm text-muted-foreground mb-6">Upload your completed work to fulfill this project</p>
      </div>
    `;
    return;
  }

  let html = '<div class="content-grid">';

  job.deliverables.forEach(deliverable => {
    html += `
      <div class="border rounded-lg p-4">
        <div class="flex justify-between items-start mb-2">
          <div>
            <p class="text-sm text-muted-foreground">Uploaded on ${formatDate(deliverable.date)}</p>
            <p class="mt-2">${deliverable.description}</p>
          </div>
          <svg class="icon text-green-500" viewBox="0 0 24 24">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </div>
        <div class="mt-4 grid grid-cols-1 gap-2">
    `;

    deliverable.files.forEach(file => {
      html += `
        <div class="file-item">
          <svg class="icon mr-2 text-primary" viewBox="0 0 24 24">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
          <div class="file-item-info">
            <p class="file-item-name">${file.name}</p>
            <p class="file-item-size">${formatFileSize(file.size)}</p>
          </div>
          <button class="button button-outline button-icon">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
          </button>
        </div>
      `;
    });

    html += `
        </div>
      </div>
    `;
  });

  html += "</div>";
  container.innerHTML = html;
}

// Update the renderWorkHistory function to use grid layout
function renderWorkHistory(history) {
  const container = workHistoryContainer;

  if (!history || history.length === 0) {
    container.innerHTML = `
      <div class="text-center py-6">
        <svg class="icon-xl mx-auto text-muted-foreground mb-4" viewBox="0 0 24 24">
          <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h4 class="text-lg font-medium mb-2">No work history yet</h4>
        <p class="text-sm text-muted-foreground">Your work history will appear here</p>
      </div>
    `;
    return;
  }

  container.innerHTML = '<div class="content-grid">';

  history.forEach(item => {
    const historyItem = document.createElement('div');
    historyItem.className = 'border rounded-lg p-4';

    let ratingHtml = '';
    if (item.rating !== null) {
      ratingHtml = `
        <div class="history-rating mt-2">
          <div class="rating">
            ${generateRatingStars(item.rating)}
          </div>
        </div>
      `;
    }

    historyItem.innerHTML = `
      <div class="history-date">${formatDate(item.date)}</div>
      <h4 class="history-title font-medium mt-1">${item.title}</h4>
      <p class="text-sm mb-2">${item.description}</p>
      <div class="flex justify-between items-center">
        <span class="history-status badge ${getStatusBadgeClass(item.status)}">${item.status}</span>
      </div>
      ${ratingHtml}
    `;

    container.querySelector('.content-grid').appendChild(historyItem);
  });

  container.querySelector('.content-grid').innerHTML += '</div>';
}

    // Helper functions
    function getJobTypeLabel(type) {
      switch (type) {
        case "time-tracking":
          return "Time Tracking";
        case "one-time":
          return "One-time Pay";
        case "milestone":
          return "Milestone";
        default:
          return "Unknown";
      }
    }

    function getJobTypeBadgeVariant(type) {
      switch (type) {
        case "time-tracking":
          return "primary";
        case "one-time":
          return "secondary";
        case "milestone":
          return "accent";
        default:
          return "default";
      }
    }

    function getMilestoneStatusBadge(status) {
      switch (status) {
        case "not_started":
          return '<span class="badge badge-outline">Not Started</span>';
        case "in_progress":
          return '<span class="badge badge-secondary">In Progress</span>';
        case "pending_approval":
          return '<span class="badge" style="background-color: #fef3c7; color: #92400e;">Pending Approval</span>';
        case "approved":
          return '<span class="badge" style="background-color: #d1fae5; color: #065f46;">Approved</span>';
        case "rejected":
          return '<span class="badge badge-destructive">Rejected</span>';
        default:
          return '<span class="badge badge-outline">Unknown</span>';
      }
    }

    function getMilestoneStatusIcon(status) {
      switch (status) {
        case "not_started":
          return '<svg class="icon text-muted-foreground" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>';
        case "in_progress":
          return '<svg class="icon text-blue-500" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>';
        case "pending_approval":
          return '<svg class="icon text-yellow-500" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>';
        case "approved":
          return '<svg class="icon text-green-500" viewBox="0 0 24 24"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>';
        case "rejected":
          return '<svg class="icon text-red-500" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>';
        default:
          return '<svg class="icon text-muted-foreground" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></svg>';
      }
    }

    // Initialize the app
    init();

    // Mobile Menu Toggle
    document.addEventListener('DOMContentLoaded', function() {
      const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
      const mobileMenu = document.getElementById('mobileMenu');

      if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', () => {
          mobileMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (event) => {
          if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
            mobileMenu.classList.remove('active');
          }
        });
      }

      // Search type dropdown
      const searchTypeBtn = document.getElementById('searchTypeBtn');
      const searchTypeDropdown = document.getElementById('searchTypeDropdown');
      const selectedSearchType = document.getElementById('selectedSearchType');
      const searchInput = document.getElementById('searchInput');

      if (searchTypeBtn && searchTypeDropdown) {
        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function() {
          searchTypeDropdown.classList.toggle('active');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
          if (!searchTypeBtn.contains(e.target) && !searchTypeDropdown.contains(e.target)) {
            searchTypeDropdown.classList.remove('active');
          }
        });
      }

      // Close dropdowns when clicking outside
      window.addEventListener('click', function(e) {
        if (!e.target.matches('.nav-dropbtn')) {
          const dropdowns = document.getElementsByClassName('nav-dropdown-content');
          for (let dropdown of dropdowns) {
            if (dropdown.classList.contains('show')) {
              dropdown.classList.remove('show');
            }
          }
        }
      });

      // Profile dropdown
      const profileButton = document.querySelector('.profile-button');
      const profileDropdownContent = document.querySelector('.profile-dropdown-content');

      if (profileButton && profileDropdownContent) {
        // Toggle dropdown on profile button click
        profileButton.addEventListener('click', function(e) {
          e.stopPropagation();
          profileDropdownContent.classList.toggle('show');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
          if (!e.target.closest('.profile-dropdown')) {
            profileDropdownContent.classList.remove('show');
          }
        });
      }
    });
  </script>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <a href="#">How to Hire</a>
                    <a href="#">Marketplace</a>
                    <a href="#">Payroll Services</a>
                    <a href="#">Service Catalog</a>
                    <a href="#">Business Networking</a>
                    <a href="#">PH Business Loan</a>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <a href="#">How It Works?</a>
                    <a href="#">Why Can't I Apply?</a>
                    <a href="#">Direct Contracts</a>
                    <a href="#">Find Mentors</a>
                    <a href="#">Mentor Application</a>
                    <a href="#">PH Health Insurance</a>
                    <a href="#">PH Life Insurance</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Help & Support</a>
                    <a href="#">News & Events</a>
                    <a href="#">Affiliate Program</a>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Charity Projects</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>
                    Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io"><i class="bi bi-facebook"></i></a>
                        <a href="https://www.instagram.com/giggenius.io/"><i class="bi bi-instagram"></i></a>
                        <a href="https://twitter.com/giggenius_io"><i class="bi bi-twitter-x"></i></a>
                        <a href="#"><i class="bi bi-tiktok"></i></a>
                        <a href="https://www.youtube.com/@giggenius"><i class="bi bi-youtube"></i></a>
                        <a href="https://www.linkedin.com/company/gig-genius/"><i class="bi bi-linkedin"></i></a>
                    </span>
                </p>
                <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="#">Terms of Service</a> |
                    <a href="#">Privacy Policy</a>
                </p>
            </div>
        </footer>
  </div>
</body>
</html>
